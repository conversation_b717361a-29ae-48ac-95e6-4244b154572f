import React, { useState, useEffect } from "react";
import { Head } from "@inertiajs/react";
import AppSidebarLayout from "@/layouts/app/app-sidebar-layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { format } from "date-fns";
import { useToast } from "@/components/ui/use-toast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, CheckCircle2, Webhook, Package,User } from "lucide-react";
import { Toaster } from "@/components/ui/toaster";
import axios from "axios";

interface Props {
    youcanConnected: boolean;
    youcanStores: { store_id: string; slug: string; is_active: boolean; is_email_verified: boolean }[];
    currentStoreId: string;
    youcanToken: string;
    integration?: any;
}

interface WebhookStatus {
    subscribed: boolean;
    url: string;
    event: string;
}

export default function YoucanImport({ youcanConnected, youcanStores, currentStoreId, youcanToken, integration }: Props) {
    const [isLoading, setIsLoading] = useState(false);
    const [isSyncingProducts, setIsSyncingProducts] = useState(false);
    const [startDate, setStartDate] = useState<Date | undefined>();
    const [endDate, setEndDate] = useState<Date | undefined>();
    const [connectionStatus, setConnectionStatus] = useState<'untested' | 'success' | 'error'>('untested');
    const [connectionMessage, setConnectionMessage] = useState('');
    const [webhookStatus, setWebhookStatus] = useState<WebhookStatus | null>(null);
    const { toast } = useToast();
    const [isSyncingCustomers, setIsSyncingCustomers] = useState(false);
    const [selectedStore, setSelectedStore] = useState(currentStoreId || "");

    // Fetch webhook status on component mount
    useEffect(() => {
        if (youcanConnected) {
            fetchWebhookStatus();
        }
    }, [youcanConnected]);

    const handleSyncCustomers = async () => {
        setIsSyncingCustomers(true);
        try {
            const response = await axios.post(route('youcan.sync-customers'));
            const data = response.data;
            toast({
                title: data.success ? "Success" : "Error",
                description: data.message,
                variant: data.success ? undefined : "destructive",
            });
        } catch (error: any) {
            toast({
                title: "Error",
                description: error.response?.data?.message || "Failed to sync customers",
                variant: "destructive",
            });
        } finally {
            setIsSyncingCustomers(false);
        }
    };
    const fetchWebhookStatus = async () => {
        try {
            const response = await axios.get(route('youcan.webhook-status'));
            setWebhookStatus(response.data);
        } catch (error) {
            console.error('Failed to fetch webhook status:', error);
        }
    };

    const testConnection = async () => {
        setIsLoading(true);
        try {
            const response = await axios.post(route('youcan.test-connection'));
            const data = response.data;

            if (data.success) {
                setConnectionStatus('success');
                setConnectionMessage(data.message);
            } else {
                setConnectionStatus('error');
                setConnectionMessage(data.message);
            }
        } catch (error) {
            setConnectionStatus('error');
            setConnectionMessage('Failed to test connection');
        } finally {
            setIsLoading(false);
        }
    };

    const handleTestImport = async () => {
        setIsLoading(true);
        try {
            const response = await axios.post(route('youcan.test-import'));
            const data = response.data;

            if (data.success) {
                toast({
                    title: "Test Import Started",
                    description: data.message,
                });
            } else {
                toast({
                    title: "Error",
                    description: data.message,
                    variant: "destructive",
                });
            }
        } catch (error: any) {
            toast({
                title: "Error",
                description: error.response?.data?.message || "Failed to start test import",
                variant: "destructive",
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleImport = async () => {
        setIsLoading(true);
        try {
            const response = await axios.post(route('youcan.import-orders'), {
                date_from: startDate ? format(startDate, 'yyyy-MM-dd') : null,
                date_to: endDate ? format(endDate, 'yyyy-MM-dd') : null,
            });

            const data = response.data;

            if (data.success) {
                toast({
                    title: "Success",
                    description: data.message,
                });
            } else {
                toast({
                    title: "Error",
                    description: data.message,
                    variant: "destructive",
                });
            }
        } catch (error: any) {
            toast({
                title: "Error",
                description: error.response?.data?.message || "Failed to initiate import",
                variant: "destructive",
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleSubscribeWebhook = async () => {
        setIsLoading(true);
        try {
            const response = await axios.post(route('youcan.webhook-subscribe'));
            const data = response.data;

            if (data.success) {
                toast({
                    title: "Success",
                    description: "Successfully subscribed to webhook",
                });
                fetchWebhookStatus();
            } else {
                toast({
                    title: "Error",
                    description: data.message || "Failed to subscribe to webhook",
                    variant: "destructive",
                });
            }
        } catch (error: any) {
            toast({
                title: "Error",
                description: error.response?.data?.message || "Failed to subscribe to webhook",
                variant: "destructive",
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleUnsubscribeWebhook = async () => {
        setIsLoading(true);
        try {
            const response = await axios.post(route('youcan.webhook-unsubscribe'));
            const data = response.data;

            if (data.success) {
                toast({
                    title: "Success",
                    description: "Successfully unsubscribed from webhook",
                });
                fetchWebhookStatus();
            } else {
                toast({
                    title: "Error",
                    description: data.message || "Failed to unsubscribe from webhook",
                    variant: "destructive",
                });
            }
        } catch (error: any) {
            toast({
                title: "Error",
                description: error.response?.data?.message || "Failed to unsubscribe from webhook",
                variant: "destructive",
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleSyncProducts = async () => {
        setIsSyncingProducts(true);
        try {
            const response = await axios.post(route('youcan.sync-products'));
            const data = response.data;

            if (data.success) {
                toast({
                    title: "Success",
                    description: data.message,
                });
            } else {
                toast({
                    title: "Error",
                    description: data.message,
                    variant: "destructive",
                });
            }
        } catch (error: any) {
            toast({
                title: "Error",
                description: error.response?.data?.message || "Failed to sync products",
                variant: "destructive",
            });
        } finally {
            setIsSyncingProducts(false);
        }
    };

    const handleSwitchStore = async (storeId: string) => {
        setIsLoading(true);
        try {
            const response = await axios.post(route('youcan.switch-store', { storeId }));
            const data = response.data;
            toast({
                title: data.success ? "Success" : "Error",
                description: data.message,
                variant: data.success ? undefined : "destructive",
            });
            if (data.success) {
                window.location.reload();
            }
        } catch (error: any) {
            toast({
                title: "Error",
                description: error.response?.data?.message || "Failed to switch store",
                variant: "destructive",
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleSyncStores = async () => {
        setIsLoading(true);
        try {
            const response = await axios.post(route('youcan.sync-stores'));
            const data = response.data;
            toast({
                title: data.success ? "Success" : "Error",
                description: data.message,
                variant: data.success ? undefined : "destructive",
            });
            if (data.success) {
                // Refresh the page to show updated stores
                window.location.reload();
            }
        } catch (error: any) {
            toast({
                title: "Error",
                description: error.response?.data?.message || "Failed to sync stores",
                variant: "destructive",
            });
        } finally {
            setIsLoading(false);
        }
    };

    // Find the current store object
    const currentStore = youcanStores?.find(store => store.store_id === currentStoreId);

    return (
        <AppSidebarLayout>
            <Head title="Import Orders from YouCan" />

            <div className="space-y-6">
                <Card>
                    <CardHeader>
                        <CardTitle>Import Orders from YouCan</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {!youcanConnected && (
                            <Alert variant="destructive">
                                <AlertCircle className="h-4 w-4" />
                                <AlertTitle>YouCan Integration Not Configured</AlertTitle>
                                <AlertDescription>
                                    <p className="mb-2">
                                        You need to configure your YouCan integration before importing orders.
                                    </p>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => window.location.href = route('cms-integrations.youcan.show')}
                                    >
                                        Configure YouCan Integration
                                    </Button>
                                </AlertDescription>
                            </Alert>
                        )}

                        {youcanConnected && (
                            <>
                                <div className="space-y-4">
                                    <div className="flex space-x-4">
                                        <Button
                                            onClick={testConnection}
                                            disabled={isLoading}
                                        >
                                            Test Connection
                                        </Button>

                                        <Button
                                            onClick={handleSyncProducts}
                                            disabled={isLoading || isSyncingProducts || connectionStatus !== 'success'}
                                            variant="outline"
                                        >
                                            <Package className="h-4 w-4 mr-2" />
                                            Sync Products
                                        </Button>
                                        <Button
    onClick={handleSyncCustomers}
    disabled={isLoading || isSyncingCustomers || connectionStatus !== 'success'}
    variant="outline"
>
    <User className="h-4 w-4 mr-2" />
    Sync Customers
</Button>
                                    </div>

                                    {connectionStatus !== 'untested' && (
                                        <Alert variant={connectionStatus === 'success' ? 'default' : 'destructive'}>
                                            {connectionStatus === 'success' ? (
                                                <CheckCircle2 className="h-4 w-4" />
                                            ) : (
                                                <AlertCircle className="h-4 w-4" />
                                            )}
                                            <AlertTitle>
                                                {connectionStatus === 'success' ? 'Connected' : 'Connection Failed'}
                                            </AlertTitle>
                                            <AlertDescription>
                                                {connectionMessage}
                                            </AlertDescription>
                                        </Alert>
                                    )}
                                </div>

                                {/* Webhook Management Section */}
                                {connectionStatus === 'success' && (
                                    <div className="space-y-4">
                                        <div className="flex items-center space-x-2">
                                            <Webhook className="h-5 w-5" />
                                            <h3 className="text-lg font-medium">Webhook Management</h3>
                                        </div>
                                        
                                        <div className="space-y-2">
                                            {webhookStatus?.subscribed ? (
                                                <>
                                                    <Alert variant="default">
                                                        <CheckCircle2 className="h-4 w-4" />
                                                        <AlertTitle>Webhook Active</AlertTitle>
                                                        <AlertDescription>
                                                            <p>Event: {webhookStatus.event}</p>
                                                            <p>URL: {webhookStatus.url}</p>
                                                        </AlertDescription>
                                                    </Alert>
                                                    <Button
                                                        onClick={handleUnsubscribeWebhook}
                                                        disabled={isLoading}
                                                        variant="destructive"
                                                    >
                                                        Unsubscribe from Webhook
                                                    </Button>
                                                </>
                                            ) : (
                                                <>
                                                    <Alert variant="warning">
                                                        <AlertCircle className="h-4 w-4" />
                                                        <AlertTitle>Webhook Not Active</AlertTitle>
                                                        <AlertDescription>
                                                            Subscribe to receive real-time order updates
                                                        </AlertDescription>
                                                    </Alert>
                                                    <Button
                                                        onClick={handleSubscribeWebhook}
                                                        disabled={isLoading}
                                                    >
                                                        Subscribe to Webhook
                                                    </Button>
                                                </>
                                            )}
                                        </div>
                                    </div>
                                )}

                                <div className="space-y-4">
                                    <div>
                                        <h3 className="text-lg font-medium">Date Range</h3>
                                        <p className="text-sm text-muted-foreground">
                                            Select a date range to import orders from (optional)
                                        </p>
                                    </div>

                                    <DateRangePicker
                                        startDate={startDate}
                                        endDate={endDate}
                                        onStartDateChange={setStartDate}
                                        onEndDateChange={setEndDate}
                                    />

                                    <div className="flex gap-2">
                                        <Button
                                            onClick={handleTestImport}
                                            disabled={isLoading || connectionStatus !== 'success'}
                                            variant="outline"
                                        >
                                            Test Import (3 orders)
                                        </Button>
                                        <Button
                                            onClick={handleImport}
                                            disabled={isLoading || connectionStatus !== 'success'}
                                        >
                                            Start Import
                                        </Button>
                                        <Button
                                            onClick={() => window.open(route('orders.index'), '_blank')}
                                            variant="secondary"
                                        >
                                            View Orders
                                        </Button>
                                    </div>
                                </div>

                                {/* Store Management Section */}
                                <div className="mb-6 p-4 border rounded-lg bg-gray-50">
                                    <div className="flex items-center justify-between mb-3">
                                        <h3 className="text-lg font-semibold">Store Management</h3>
                                        <Button
                                            onClick={handleSyncStores}
                                            disabled={isLoading}
                                            variant="outline"
                                            size="sm"
                                        >
                                            Sync Stores
                                        </Button>
                                    </div>

                                    {currentStore && (
                                        <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded">
                                            <div className="font-semibold text-blue-800">
                                                Current Store: {currentStore.store_name || currentStore.slug}
                                            </div>
                                            <div className="text-sm text-blue-600">
                                                ID: {currentStore.store_id} |
                                                Status: {currentStore.is_active ? 'Active' : 'Inactive'}
                                                {currentStore.store_url && (
                                                    <> | URL: <a href={currentStore.store_url} target="_blank" rel="noopener noreferrer" className="underline">{currentStore.store_url}</a></>
                                                )}
                                            </div>
                                        </div>
                                    )}

                                    <div className="flex gap-2 items-end">
                                        <div className="flex-1">
                                            <label className="block mb-1 text-sm font-medium">Choose Store:</label>
                                            <select
                                                value={selectedStore || ""}
                                                onChange={e => setSelectedStore(e.target.value)}
                                                disabled={isLoading}
                                                className="w-full border rounded px-3 py-2 text-sm"
                                            >
                                                <option value="">Select a store...</option>
                                                {youcanStores?.map(store => (
                                                    <option key={store.store_id} value={store.store_id}>
                                                        {store.store_name || store.slug}
                                                        {store.is_active ? ' (Active)' : ' (Inactive)'}
                                                        {store.store_id === currentStoreId ? ' - Current' : ''}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>
                                        <Button
                                            onClick={() => handleSwitchStore(selectedStore)}
                                            disabled={isLoading || !selectedStore || selectedStore === currentStoreId}
                                            variant="outline"
                                        >
                                            Switch Store
                                        </Button>
                                    </div>

                                    {youcanStores && youcanStores.length > 0 && (
                                        <div className="mt-3 text-sm text-gray-600">
                                            Total stores available: {youcanStores.length}
                                        </div>
                                    )}
                                </div>
                            </>
                        )}
                    </CardContent>
                </Card>
            </div>
            <Toaster />
        </AppSidebarLayout>
    );
} 