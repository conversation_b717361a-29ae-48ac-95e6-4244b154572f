<?php

namespace App\Jobs;

use App\Models\Order;
use App\Models\Customer;
use App\Models\OrderStatus;
use App\Services\YoucanAPI\YoucanService;
use App\Services\CmsIntegration\YoucanCmsService;
use App\Models\CompanyCmsIntegration;
use App\Models\CmsPlatform;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\Product;
use App\Models\OrderItem;
use App\Models\Country;
use App\Models\State;
use App\Models\City;
use App\Services\GeographicMappingService;

class SyncYoucanOrders implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $companyId;
    protected $params;
    protected $userId;

    /**
     * Create a new job instance.
     */
    public function __construct(int $companyId, array $params = [], ?int $userId = null)
    {
        $this->companyId = $companyId;
        $this->params = $params;
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     */
    public function handle()
    {
        try {
            Log::info('Starting YouCan orders sync for company: ' . $this->companyId);

            // Set the company_id in the session for the job context
            session(['company_id' => $this->companyId]);

            // Create a fake authenticated user with the company_id for the job context
            $user = new \App\Models\User();
            $user->company_id = $this->companyId;
            auth()->setUser($user);

            // Get YouCan integration for this company
            $platform = CmsPlatform::bySlug('youcan')->first();
            if (!$platform) {
                throw new \Exception('YouCan platform not found');
            }

            $integration = CompanyCmsIntegration::forCompany($this->companyId)
                ->forPlatform($platform->id)
                ->active()
                ->first();

            if (!$integration) {
                // Fallback to old service for backward compatibility
                $youcanService = app(YoucanService::class);
                $orders = $youcanService->getOrders($this->params);
            } else {
                // Use new CMS service
                $youcanService = new YoucanCmsService($integration);
                $orders = $youcanService->getOrders($this->params);
            }

            if (empty($orders['data'])) {
                Log::info('No orders found to sync');
                return;
            }

            foreach ($orders['data'] as $orderData) {
                DB::beginTransaction();
                try {
                    $this->processOrder($orderData);
                    DB::commit();
                } catch (\Exception $e) {
                    DB::rollBack();
                    Log::error('Error processing order: ' . $e->getMessage());
                    continue;
                }
            }

            //If there are more pages, process them
           /* if (!empty($orders['meta']['pagination']['links']['next'])) {
                $this->params['page'] = ($this->params['page'] ?? 1) + 1;
                self::dispatch($this->companyId, $this->params, $this->userId);
            }*/

            Log::info('YouCan orders sync completed');

        } catch (\Exception $e) {
            Log::error('YouCan sync failed: ' . $e->getMessage());
            throw $e;
        } finally {
            // Clear the company context
            session()->forget('company_id');
            auth()->logout();
        }
    }

    /**
     * Process a single order from YouCan
     */
    protected function processOrder(array $orderData)
    {
        try {
            Log::info('Processing order data:', [
                'order_id' => $orderData['id'],
                'ref' => $orderData['ref'],
                'status' => $orderData['status']
            ]);

            // Check if order already exists using multiple criteria for better duplicate detection
            $existingOrder = $this->findExistingOrder($orderData);
            if ($existingOrder) {
                Log::info('Order already exists, skipping:', [
                    'ref' => $orderData['ref'],
                    'youcan_order_id' => $orderData['id'],
                    'existing_order_id' => $existingOrder->id
                ]);
                return;
            }

            // Get the default status for the company
            $status = $this->getOrCreateDefaultOrderStatus();

            // Extract address data from YouCan order
            $addressData = $this->extractAddressData($orderData);

            // Find or create customer with intelligent duplicate detection
            $customer = $this->findOrCreateCustomer($orderData, $addressData);

            // Create the order with YouCan tracking fields
            $order = $this->createOrder($orderData, $customer, $status, $addressData);

            Log::info('Order created successfully:', ['order_id' => $order->id]);

            // Process order items if they exist
            $this->processOrderItems($orderData, $order);

            Log::info('Order processing completed successfully:', [
                'order_id' => $order->id,
                'ref' => $orderData['ref']
            ]);

        } catch (\Exception $e) {
            Log::error('Error processing order: ' . $e->getMessage(), [
                'order_id' => $orderData['id'] ?? 'unknown',
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Find existing order using multiple criteria
     */
    protected function findExistingOrder(array $orderData): ?Order
    {
        // First check by YouCan order ID (most reliable)
        if (!empty($orderData['id'])) {
            $existing = Order::where('company_id', $this->companyId)
                ->where('youcan_order_id', $orderData['id'])
                ->first();
            if ($existing) {
                return $existing;
            }
        }

        // Then check by ref number
        if (!empty($orderData['ref'])) {
            $existing = Order::where('company_id', $this->companyId)
                ->where('ref', $orderData['ref'])
                ->first();
            if ($existing) {
                // Update with YouCan ID if missing
                if (empty($existing->youcan_order_id) && !empty($orderData['id'])) {
                    $existing->update(['youcan_order_id' => $orderData['id']]);
                }
                return $existing;
            }
        }

        return null;
    }

    /**
     * Get or create default order status
     */
    protected function getOrCreateDefaultOrderStatus(): OrderStatus
    {
        $status = OrderStatus::where('company_id', $this->companyId)
            ->where('is_default', true)
            ->first();

        if (!$status) {
            Log::info('No default order status found, creating default status for company: ' . $this->companyId);
            $status = OrderStatus::create([
                'company_id' => $this->companyId,
                'name' => 'en attente',
                'color' => '#FFA500',
                'is_active' => true,
                'is_default' => true,
                'display_name' => 'En attente'
            ]);
        }

        Log::info('Using default order status:', [
            'status_id' => $status->id,
            'status_name' => $status->name
        ]);

        return $status;
    }

    /**
     * Extract address data from YouCan order
     */
    protected function extractAddressData(array $orderData): array
    {
        // Extract address from shipping or payment address
        $shippingAddress = $orderData['shipping']['address'] ?? [];
        $paymentAddress = $orderData['payment']['address'] ?? [];

        // Prefer shipping address, fallback to payment address
        $address = !empty($shippingAddress) ? $shippingAddress : $paymentAddress;

        // Extract geographic data from YouCan extra_fields and custom_fields
        $extraFields = $orderData['extra_fields'] ?? [];
        $customFields = $orderData['custom_fields'] ?? [];

        // YouCan often stores region/state in extra_fields or custom_fields
        $regionFromExtra = null;
        foreach ($extraFields as $key => $value) {
            if (stripos($key, 'gouvernorat') !== false || stripos($key, 'region') !== false || stripos($key, 'state') !== false) {
                $regionFromExtra = $value;
                break;
            }
        }

        $regionFromCustom = $customFields['region'] ?? $customFields['state'] ?? $customFields['gouvernorat'] ?? null;

        // Clean up Arabic generic terms that don't represent actual state names
        $arabicGenericTerms = ['الولاية', 'المحافظة', 'المنطقة']; // "the state", "the governorate", "the region"

        if ($regionFromExtra && in_array(trim($regionFromExtra), $arabicGenericTerms)) {
            $regionFromExtra = null;
        }

        if ($regionFromCustom && in_array(trim($regionFromCustom), $arabicGenericTerms)) {
            $regionFromCustom = null;
        }

        // Try different address line fields that YouCan might use
        $addressLine = $address['first_line'] ??
                      $address['address_line'] ??
                      $address['address'] ??
                      $address['line1'] ??
                      $address['street'] ??
                      null;

        // Extract country information - YouCan orders are typically from Tunisia
        $countryCode = $address['country_code'] ?? 'TN'; // Default to Tunisia
        $countryName = $address['country_name'] ?? 'Tunisia';

        // Extract state/region information
        $state = $address['state'] ??
                 $address['region'] ??
                 $regionFromExtra ??
                 $regionFromCustom ??
                 null;

        // Extract city information
        $city = $address['city'] ?? null;

        // If still no address, try to construct from available data
        if (empty($addressLine)) {
            $addressParts = array_filter([
                $city,
                $state,
                $countryName
            ]);
            $addressLine = !empty($addressParts) ? implode(', ', $addressParts) : 'Address not provided';
        }

        // Extract phone from customer data if not in address
        $phone = $address['phone'] ?? $orderData['customer']['phone'] ?? null;

        Log::info('Extracted address data from YouCan order:', [
            'youcan_order_id' => $orderData['id'] ?? 'unknown',
            'extracted_data' => [
                'country_code' => $countryCode,
                'country_name' => $countryName,
                'state' => $state,
                'city' => $city,
                'address_line' => $addressLine,
                'phone' => $phone,
            ],
            'source_data' => [
                'shipping_address' => $shippingAddress,
                'payment_address' => $paymentAddress,
                'extra_fields' => $extraFields,
                'custom_fields' => $customFields,
            ]
        ]);

        return [
            'country_code' => $countryCode,
            'country_name' => $countryName,
            'state' => $state,
            'city' => $city,
            'address_line' => $addressLine,
            'zip_code' => $address['zip_code'] ?? null,
            'phone' => $phone,
        ];
    }

    /**
     * Resolve geographic IDs from names using intelligent mapping service
     */
    protected function resolveGeographicIds(array $addressData): array
    {
        $geoService = new GeographicMappingService();
        $geoIds = $geoService->resolveGeographicIds($addressData);

        // Always log geographic resolution for debugging YouCan orders
        $geoInfo = $geoService->getGeographicInfo($geoIds);

        Log::info('Geographic data resolved for YouCan order:', [
            'input' => [
                'country_code' => $addressData['country_code'] ?? null,
                'country_name' => $addressData['country_name'] ?? null,
                'state' => $addressData['state'] ?? null,
                'city' => $addressData['city'] ?? null,
            ],
            'resolved' => $geoIds,
            'names' => $geoInfo,
            'has_nulls' => [
                'country_id_null' => is_null($geoIds['country_id']),
                'state_id_null' => is_null($geoIds['state_id']),
                'city_id_null' => is_null($geoIds['city_id']),
            ]
        ]);

        return $geoIds;
    }

    /**
     * Find or create customer with intelligent duplicate detection
     */
    protected function findOrCreateCustomer(array $orderData, array $addressData): Customer
    {
        $customerData = $orderData['customer'] ?? [];
        $email = $customerData['email'] ?? null;
        $phone = $addressData['phone'] ?? $customerData['phone'] ?? null;
        $fullName = $customerData['full_name'] ?? 'Unknown Customer';

        // Parse phone number and country code
        $phoneData = $this->parsePhoneNumber($phone, $addressData['country_code'] ?? null);

        // Try to find existing customer by phone first (most reliable)
        $customer = null;
        if ($phoneData['phone'] && $phoneData['phone_code']) {
            $customer = Customer::where('company_id', $this->companyId)
                ->where('primary_phone', $phoneData['phone'])
                ->where('primary_phonecode', $phoneData['phone_code'])
                ->first();
        }

        // If not found by phone, try by email
        if (!$customer && $email) {
            $customer = Customer::where('company_id', $this->companyId)
                ->where('email', $email)
                ->first();
        }

        // If not found by phone or email, try by name and address combination
        if (!$customer && !empty($addressData['address_line'])) {
            $customer = Customer::where('company_id', $this->companyId)
                ->where('full_name', $fullName)
                ->where('address', 'LIKE', '%' . substr($addressData['address_line'], 0, 20) . '%')
                ->first();
        }

        // If customer found, update with any missing YouCan data
        if ($customer) {
            $updateData = [];

            if (empty($customer->youcan_customer_id) && !empty($customerData['id'])) {
                $updateData['youcan_customer_id'] = $customerData['id'];
            }

            if (!empty($updateData)) {
                $customer->update($updateData);
            }

            return $customer;
        }

        // Resolve geographic IDs
        $geoIds = $this->resolveGeographicIds($addressData);

        // Create new customer
        $customer = Customer::create([
            'company_id' => $this->companyId,
            'email' => $email,
            'full_name' => $fullName,
            'primary_phone' => $phoneData['phone'],
            'primary_phonecode' => $phoneData['phone_code'] ?? '216', // Default to 216 if null
            'address' => $addressData['address_line'],
            'postal_code' => $addressData['zip_code'],
            'country_id' => $geoIds['country_id'],
            'state_id' => $geoIds['state_id'],
            'city_id' => $geoIds['city_id'],
            'youcan_customer_id' => $customerData['id'] ?? null,
            'youcan_synced_at' => now(),
            // 'status' => 'active', // Removed due to database sync issue
        ]);

        Log::info('Created new customer from YouCan order:', [
            'customer_id' => $customer->id,
            'email' => $email,
            'phone' => $phoneData['phone_code'] . $phoneData['phone'],
            'youcan_customer_id' => $customerData['id'] ?? null,
        ]);

        return $customer;
    }

    /**
     * Parse phone number and extract country code
     */
    protected function parsePhoneNumber(?string $phone, ?string $countryCode): array
    {
        if (empty($phone)) {
            return ['phone' => null, 'phone_code' => '216']; // Default to Tunisia phone code
        }

        // Clean the phone number
        $cleanPhone = preg_replace('/[^\d+]/', '', $phone);

        // If phone starts with +, extract country code
        if (str_starts_with($cleanPhone, '+')) {
            // Remove the + sign
            $cleanPhone = substr($cleanPhone, 1);

            // Try to extract country code based on known patterns
            $phoneCodeMap = $this->getPhoneCodeMap();

            foreach ($phoneCodeMap as $code => $countries) {
                if (str_starts_with($cleanPhone, $code)) {
                    // Check if the country matches (if provided)
                    if ($countryCode && in_array(strtoupper($countryCode), $countries)) {
                        return [
                            'phone' => substr($cleanPhone, strlen($code)),
                            'phone_code' => $code
                        ];
                    }
                    // If no country code provided or first match
                    if (!$countryCode) {
                        return [
                            'phone' => substr($cleanPhone, strlen($code)),
                            'phone_code' => $code
                        ];
                    }
                }
            }
        }

        // If no + prefix or couldn't parse, try to get country code from country
        if ($countryCode) {
            $phoneCodeFromCountry = $this->getPhoneCodeFromCountry($countryCode);
            if ($phoneCodeFromCountry) {
                // Remove country code if phone starts with it
                if (str_starts_with($cleanPhone, $phoneCodeFromCountry)) {
                    $cleanPhone = substr($cleanPhone, strlen($phoneCodeFromCountry));
                }
                return [
                    'phone' => $cleanPhone,
                    'phone_code' => $phoneCodeFromCountry
                ];
            }
        }

        // Fallback: return phone as-is with default country code (Tunisia)
        return [
            'phone' => $cleanPhone,
            'phone_code' => '216' // Default to Tunisia phone code
        ];
    }

    /**
     * Get phone code from country ISO2 code
     */
    protected function getPhoneCodeFromCountry(string $countryCode): ?string
    {
        $country = Country::where('iso2', strtoupper($countryCode))->first();
        return $country ? $country->phonecode : null;
    }

    /**
     * Get phone code mapping for parsing
     */
    protected function getPhoneCodeMap(): array
    {
        return [
            '1' => ['US', 'CA'], // USA, Canada
            '33' => ['FR'], // France
            '34' => ['ES'], // Spain
            '39' => ['IT'], // Italy
            '44' => ['GB'], // UK
            '49' => ['DE'], // Germany
            '212' => ['MA'], // Morocco
            '213' => ['DZ'], // Algeria
            '216' => ['TN'], // Tunisia
            '218' => ['LY'], // Libya
            '220' => ['GM'], // Gambia
            '221' => ['SN'], // Senegal
            '222' => ['MR'], // Mauritania
            '223' => ['ML'], // Mali
            '224' => ['GN'], // Guinea
            '225' => ['CI'], // Ivory Coast
            '226' => ['BF'], // Burkina Faso
            '227' => ['NE'], // Niger
            '228' => ['TG'], // Togo
            '229' => ['BJ'], // Benin
            '230' => ['MU'], // Mauritius
            '231' => ['LR'], // Liberia
            '232' => ['SL'], // Sierra Leone
            '233' => ['GH'], // Ghana
            '234' => ['NG'], // Nigeria
            '235' => ['TD'], // Chad
            '236' => ['CF'], // Central African Republic
            '237' => ['CM'], // Cameroon
            '238' => ['CV'], // Cape Verde
            '239' => ['ST'], // São Tomé and Príncipe
            '240' => ['GQ'], // Equatorial Guinea
            '241' => ['GA'], // Gabon
            '242' => ['CG'], // Republic of the Congo
            '243' => ['CD'], // Democratic Republic of the Congo
            '244' => ['AO'], // Angola
            '245' => ['GW'], // Guinea-Bissau
            '246' => ['IO'], // British Indian Ocean Territory
            '247' => ['AC'], // Ascension Island
            '248' => ['SC'], // Seychelles
            '249' => ['SD'], // Sudan
            '250' => ['RW'], // Rwanda
            '251' => ['ET'], // Ethiopia
            '252' => ['SO'], // Somalia
            '253' => ['DJ'], // Djibouti
            '254' => ['KE'], // Kenya
            '255' => ['TZ'], // Tanzania
            '256' => ['UG'], // Uganda
            '257' => ['BI'], // Burundi
            '258' => ['MZ'], // Mozambique
            '260' => ['ZM'], // Zambia
            '261' => ['MG'], // Madagascar
            '262' => ['RE', 'YT'], // Réunion, Mayotte
            '263' => ['ZW'], // Zimbabwe
            '264' => ['NA'], // Namibia
            '265' => ['MW'], // Malawi
            '266' => ['LS'], // Lesotho
            '267' => ['BW'], // Botswana
            '268' => ['SZ'], // Eswatini
            '269' => ['KM'], // Comoros
            '290' => ['SH'], // Saint Helena
            '291' => ['ER'], // Eritrea
            '297' => ['AW'], // Aruba
            '298' => ['FO'], // Faroe Islands
            '299' => ['GL'], // Greenland
        ];
    }

    /**
     * Create order with YouCan tracking data
     */
    protected function createOrder(array $orderData, Customer $customer, OrderStatus $status, array $addressData): Order
    {
        // Resolve geographic IDs for order address
        $geoIds = $this->resolveGeographicIds($addressData);

        // CRITICAL: Ensure we have valid geographic IDs - apply additional safety check
        // This is essential to prevent null constraint violations
        if (empty($geoIds['city_id']) || empty($geoIds['state_id']) || empty($geoIds['country_id'])) {
            Log::warning('Geographic IDs are missing, applying emergency fallback', [
                'original_geoIds' => $geoIds,
                'address_data' => $addressData,
                'youcan_order_id' => $orderData['id'] ?? 'unknown'
            ]);

            // Emergency fallback - get first available city/state
            $fallbackCity = \App\Models\City::first();
            $fallbackState = \App\Models\State::first();
            $fallbackCountry = \App\Models\Country::where('iso2', 'TN')->first() ?? \App\Models\Country::first();

            $geoIds = [
                'country_id' => $geoIds['country_id'] ?? $fallbackCountry->iso2,
                'state_id' => $geoIds['state_id'] ?? $fallbackState->id,
                'city_id' => $geoIds['city_id'] ?? $fallbackCity->id,
            ];

            Log::info('Emergency fallback applied', ['final_geoIds' => $geoIds]);
        }

        // FINAL SAFETY CHECK: Ensure no null values before database insertion
        if (is_null($geoIds['city_id']) || is_null($geoIds['state_id']) || is_null($geoIds['country_id'])) {
            Log::error('CRITICAL: Geographic IDs are still null after fallback, using absolute defaults', [
                'geoIds' => $geoIds,
                'youcan_order_id' => $orderData['id'] ?? 'unknown'
            ]);

            // Absolute fallback - use hardcoded values to prevent database errors
            $geoIds = [
                'country_id' => $geoIds['country_id'] ?? 'TN',
                'state_id' => $geoIds['state_id'] ?? 2550, // Ariana state ID
                'city_id' => $geoIds['city_id'] ?? 106915, // Ariana city ID
            ];

            Log::info('Absolute fallback applied', ['final_geoIds' => $geoIds]);
        }

        // Parse phone for order
        $phoneData = $this->parsePhoneNumber(
            $addressData['phone'] ?? $customer->primary_phone,
            $addressData['country_code'] ?? null
        );

        // Log the data being used for order creation
        Log::info('Creating order with geographic data', [
            'youcan_order_id' => $orderData['id'],
            'customer_id' => $customer->id,
            'geoIds' => $geoIds,
            'phoneData' => $phoneData
        ]);

        $order = Order::create([
            'company_id' => $this->companyId,
            'id_client' => $customer->id,
            'total_price' => $orderData['total'] ?? 0,
            'order_status_id' => $status->id,
            'client_name' => $customer->full_name,
            'client_adress' => $addressData['address_line'] ?? 'Address not provided',
            'client_tel_1' => $phoneData['phone'],
            'client_phonecode_1' => $phoneData['phone_code'] ?? '216',
            'client_city_id' => $geoIds['city_id'],
            'client_state_id' => $geoIds['state_id'],
            'notes' => $orderData['notes'],
            'ref' => $orderData['ref'],
            'youcan_order_id' => $orderData['id'],
            'youcan_order_data' => $orderData, // Store full YouCan order data for reference
            'youcan_synced_at' => now(),
            'source' => 'youcan',
            'is_running' => true,
            // Get or create YouCan ad platform and campaign IDs
            'ad_platform_id' => $this->getYoucanAdPlatformId(),
            'ad_campaign_id' => $this->getYoucanAdCampaignId(),
            'created_at' => isset($orderData['created_at']) ?
                \Carbon\Carbon::createFromTimestamp($orderData['created_at']) : now(),
            'updated_at' => isset($orderData['updated_at']) ?
                \Carbon\Carbon::createFromTimestamp($orderData['updated_at']) : now(),
        ]);

        return $order;
    }

    /**
     * Process order items from YouCan order data
     */
    protected function processOrderItems(array $orderData, Order $order): void
    {
        // YouCan orders might have variants or line items
        $variants = $orderData['variants'] ?? [];

        if (empty($variants)) {
            Log::info('No variants found in order, skipping item processing', [
                'order_id' => $order->id,
                'youcan_order_id' => $orderData['id']
            ]);
            return;
        }

        foreach ($variants as $variant) {
            try {
                $this->processOrderItem($variant, $order);
            } catch (\Exception $e) {
                Log::error('Error processing order item: ' . $e->getMessage(), [
                    'variant_data' => $variant,
                    'order_id' => $order->id,
                    'trace' => $e->getTraceAsString()
                ]);
                // Continue processing other items
                continue;
            }
        }

        Log::info('Order items processed successfully', [
            'order_id' => $order->id,
            'items_count' => count($variants)
        ]);
    }

    /**
     * Process a single order item
     */
    protected function processOrderItem(array $variant, Order $order): void
    {
        $productData = $variant['variant']['product'] ?? [];

        if (empty($productData)) {
            Log::warning('Missing product data for variant', [
                'variant_id' => $variant['id'] ?? 'unknown',
                'order_id' => $order->id
            ]);
            return;
        }

        // Find or create product
        $product = $this->findOrCreateProduct($productData, $variant);

        // Create order item
        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'item_name' => $productData['name'] ?? 'Unknown Product',
            'quantity' => $variant['quantity'] ?? 1,
            'unit_price' => $variant['price'] ?? 0,
            'amount' => ($variant['price'] ?? 0) * ($variant['quantity'] ?? 1),
            'company_id' => $this->companyId,
        ]);
    }

    /**
     * Find or create product from YouCan data
     */
    protected function findOrCreateProduct(array $productData, array $variant): Product
    {
        // Try to find existing product by YouCan product ID
        $product = Product::where('company_id', $this->companyId)
            ->where('youcan_product_id', $productData['id'])
            ->first();

        if ($product) {
            return $product;
        }

        // Try to find by SKU/code
        if (!empty($productData['sku'])) {
            $product = Product::where('company_id', $this->companyId)
                ->where('code', $productData['sku'])
                ->first();

            if ($product) {
                // Update with YouCan ID
                $product->update(['youcan_product_id' => $productData['id']]);
                return $product;
            }
        }

        // Create new product
        $product = Product::create([
            'company_id' => $this->companyId,
            'name' => $productData['name'] ?? 'Unknown Product',
            'code' => $productData['sku'] ?? 'YC-' . $productData['id'],
            'product_type' => 'standard',
            'sell_price' => $variant['price'] ?? 0,
            'total_qty' => 0,
            'alert_quantity' => 0,
            'status' => 'active',
            'youcan_product_id' => $productData['id'],
            'youcan_synced_at' => now(),
        ]);

        Log::info('Created new product from YouCan order:', [
            'product_id' => $product->id,
            'youcan_product_id' => $productData['id'],
            'name' => $productData['name']
        ]);

        return $product;
    }

    /**
     * Get or create YouCan ad platform ID
     */
    protected function getYoucanAdPlatformId(): int
    {
        $platform = \App\Models\AdPlatform::where('company_id', $this->companyId)
            ->where('platform_name', 'YouCan')
            ->first();

        if (!$platform) {
            $platform = \App\Models\AdPlatform::create([
                'platform_name' => 'YouCan',
                'platform_type' => 'E-commerce',
                'company_id' => $this->companyId,
            ]);

            Log::info('Created YouCan ad platform', [
                'platform_id' => $platform->id,
                'company_id' => $this->companyId
            ]);
        }

        return $platform->id;
    }

    /**
     * Get or create YouCan ad campaign ID
     */
    protected function getYoucanAdCampaignId(): int
    {
        $platformId = $this->getYoucanAdPlatformId();

        $campaign = \App\Models\AdCampaign::where('ad_platform_id', $platformId)
            ->where('company_id', $this->companyId)
            ->where('campaign_name', 'YouCan Orders')
            ->first();

        if (!$campaign) {
            $campaign = \App\Models\AdCampaign::create([
                'ad_platform_id' => $platformId,
                'campaign_name' => 'YouCan Orders',
                'start_date' => now()->toDateString(),
                'end_date' => now()->addYear()->toDateString(),
                'budget' => 0.00,
                'sync' => false,
                'company_id' => $this->companyId,
            ]);

            Log::info('Created YouCan ad campaign', [
                'campaign_id' => $campaign->id,
                'platform_id' => $platformId,
                'company_id' => $this->companyId
            ]);
        }

        return $campaign->id;
    }
}