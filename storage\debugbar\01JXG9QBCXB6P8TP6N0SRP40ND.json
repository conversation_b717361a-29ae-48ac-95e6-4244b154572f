{"__meta": {"id": "01JXG9QBCXB6P8TP6N0SRP40ND", "datetime": "2025-06-11 20:08:24", "utime": **********.735291, "method": "GET", "uri": "/suppliers", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.05712, "end": **********.735335, "duration": 0.6782150268554688, "duration_str": "678ms", "measures": [{"label": "Booting", "start": **********.05712, "relative_start": 0, "end": **********.288641, "relative_end": **********.288641, "duration": 0.****************, "duration_str": "232ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.28868, "relative_start": 0.*****************, "end": **********.73534, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "447ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.321436, "relative_start": 0.****************, "end": **********.338088, "relative_end": **********.338088, "duration": 0.*****************, "duration_str": "16.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.62101, "relative_start": 0.****************, "end": **********.730751, "relative_end": **********.730751, "duration": 0.****************, "duration_str": "110ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 4770184, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.7.2", "PHP Version": "8.2.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "Suppliers/Index", "param_count": null, "params": [], "start": **********.734678, "type": "tsx", "hash": "tsxC:\\Users\\<USER>\\Desktop\\react-starter-kit\\resources\\js/Pages/Suppliers/Index.tsxSuppliers/Index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fresources%2Fjs%2FPages%2FSuppliers%2FIndex.tsx&line=1", "ajax": false, "filename": "Index.tsx", "line": "?"}}]}, "queries": {"count": 6, "nb_statements": 5, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.16011, "accumulated_duration_str": "160ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 226}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 112}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 92}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 60}, {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 88}], "start": **********.319057, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CacheManager.php:226", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheManager.php&line=226", "ajax": false, "filename": "CacheManager.php", "line": "226"}, "connection": "order_management_system", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = 'fjlxYCyxOlRCexdXwC4uopqp4OZNpD9IdJe0ZzTn' limit 1", "type": "query", "params": [], "bindings": ["fjlxYCyxOlRCexdXwC4uopqp4OZNpD9IdJe0ZzTn"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.3872242, "duration": 0.13406, "duration_str": "134ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "order_management_system", "explain": null, "start_percent": 0, "width_percent": 83.73}, {"sql": "select * from \"users\" where \"id\" = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.543649, "duration": 0.01276, "duration_str": "12.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "order_management_system", "explain": null, "start_percent": 83.73, "width_percent": 7.97}, {"sql": "select * from \"companies\" where \"companies\".\"id\" = 1 and \"companies\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Middleware/LoadAppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Middleware\\LoadAppSettings.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Middleware/AddLinkHeadersForPreloadedAssets.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php", "line": 20}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 86}], "start": **********.574591, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "LoadAppSettings.php:20", "source": {"index": 21, "namespace": null, "name": "app/Http/Middleware/LoadAppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Middleware\\LoadAppSettings.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FMiddleware%2FLoadAppSettings.php&line=20", "ajax": false, "filename": "LoadAppSettings.php", "line": "20"}, "connection": "order_management_system", "explain": null, "start_percent": 91.699, "width_percent": 2.554}, {"sql": "select count(*) as aggregate from \"suppliers\" where \"company_id\" = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SupplierController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\SupplierController.php", "line": 42}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.589747, "duration": 0.00763, "duration_str": "7.63ms", "memory": 0, "memory_str": null, "filename": "SupplierController.php:42", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/SupplierController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\SupplierController.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FSupplierController.php&line=42", "ajax": false, "filename": "SupplierController.php", "line": "42"}, "connection": "order_management_system", "explain": null, "start_percent": 94.254, "width_percent": 4.765}, {"sql": "select * from \"suppliers\" where \"company_id\" = 1 order by \"created_at\" desc limit 10 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SupplierController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\SupplierController.php", "line": 42}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.606108, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "SupplierController.php:42", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/SupplierController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\SupplierController.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FSupplierController.php&line=42", "ajax": false, "filename": "SupplierController.php", "line": "42"}, "connection": "order_management_system", "explain": null, "start_percent": 99.019, "width_percent": 0.981}]}, "models": {"data": {"App\\Models\\Supplier": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FSupplier.php&line=1", "ajax": false, "filename": "Supplier.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Company": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}}, "count": 8, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/suppliers", "action_name": "suppliers.index", "controller_action": "App\\Http\\Controllers\\SupplierController@index", "uri": "GET suppliers", "controller": "App\\Http\\Controllers\\SupplierController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FSupplierController.php&line=13\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FSupplierController.php&line=13\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/SupplierController.php:13-48</a>", "middleware": "web, auth, verified", "duration": "685ms", "peak_memory": "6MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-804212772 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-804212772\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1780562035 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1780562035\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1642371389 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlhhcVhnNGV3UG9TbURjdHcyekxyVFE9PSIsInZhbHVlIjoiVkNHV0NFTXJjWFJvblFFR1hsdHdUQngzTUIwWVJDaE9sYnQrYlZCK2VORzFmK3BGbENIMTVNOHRZZ2NEb0ZpNW4yTExjRWxZREJyRHFlMTJxTkpjUUd3T0s1NS9uUE5DeVVUWHpEdTFqZHJyRGlQcytwSCtKK3hoU2Q1R1dPTG0iLCJtYWMiOiI3MTNmOWNmMDdhZWYxMGRiYmE5ZTVjNjQzMzI1MDRlY2YyZDBjYWRlOTljZGIwZDdmNGMyMTRhMDQ1OTAxZDIxIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">70490311fe7c84acda8886406a6d884b</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1603 characters\">_cc_cookie={&quot;categories&quot;:[&quot;necessary&quot;],&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false,&quot;consent_date&quot;:&quot;2025-02-09T00:01:04.160Z&quot;,&quot;consent_uuid&quot;:&quot;410c5564-6d38-4cde-b65a-95ec6886d6c1&quot;,&quot;last_consent_update&quot;:&quot;2025-02-09T00:01:04.160Z&quot;}; locale=en; appearance=light; device_uuid=eyJpdiI6IkducU5Ec2dRdUU2RldQVXpEZjBOaWc9PSIsInZhbHVlIjoiR1hpL2FPTldjeDhodmY1MC92aXV5WXFpeGNiQXpBRWV2VVRkK0IyeHJWSTBoVHF0Nkc4Tjc0VXNWa3BSMHNHWElXa2NrSUd6QjhZSzdnTkpkaEQ0WmtGWWppVGthN293K1N2dHhDZnVkU1ZsNlQ2QXV4VnE0K29COXBnVWQwTktwU3pBb25TeFdIN0l4TWZXbDlUaDdCSndkck5TUW02blptaVVBbXpweVBLOHJFNks3VUxpMU82MkpjenVuUGFPIiwibWFjIjoiZDUwYjkyOWU2OTBiZDhjZGZiOWJhN2ZjM2M4ZGFhMWM0OTcxY2VkMDhkNjlkMWZkMjU3Yzk5MGM1Mzk4ZWFiNyIsInRhZyI6IiJ9; admin-mode=dark; admin-colorPref=dark; direction=false; _ga=GA1.1.1790330909.1748982694; _ga_699NE13B0K=GS2.1.s1749281199$o2$g0$t1749281199$j60$l0$h0; sidebar_state=true; XSRF-TOKEN=eyJpdiI6IlhhcVhnNGV3UG9TbURjdHcyekxyVFE9PSIsInZhbHVlIjoiVkNHV0NFTXJjWFJvblFFR1hsdHdUQngzTUIwWVJDaE9sYnQrYlZCK2VORzFmK3BGbENIMTVNOHRZZ2NEb0ZpNW4yTExjRWxZREJyRHFlMTJxTkpjUUd3T0s1NS9uUE5DeVVUWHpEdTFqZHJyRGlQcytwSCtKK3hoU2Q1R1dPTG0iLCJtYWMiOiI3MTNmOWNmMDdhZWYxMGRiYmE5ZTVjNjQzMzI1MDRlY2YyZDBjYWRlOTljZGIwZDdmNGMyMTRhMDQ1OTAxZDIxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjF5b0lDU2UzblpJZFdZMkRqNjNoV0E9PSIsInZhbHVlIjoibFRVdkdJS1BUMDZHa05MSjdMSkVLa0hKbVdnNzVxL0FWbk9SY25BRVh0Ync2ZkxpSFNpQ0VZMmpERkhoYjE2dTZmSUJSR1pmNnpUZzd2WkNvaGN2MTBQNFdmeEZDbVBOVXVGVHRkM25JdlRFTHhFSldSNWFYSmJFcndyYmlWRHgiLCJtYWMiOiIyM2MxYTZlM2NlYTk4M2Y3NWM2MjI2NWVjZWU4MjFlMzg0NTZmZTZhODBiNTE0NzY4NTcwZWU1ZjI1MWJiZDM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1642371389\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>device_uuid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-colorPref</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>direction</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_699NE13B0K</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sidebar_state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kWRbVKDkrren5vQ8ASRAS7kZbZAeToYK9Kqbk5Cb</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fjlxYCyxOlRCexdXwC4uopqp4OZNpD9IdJe0ZzTn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1844397536 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 11 Jun 2025 20:08:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1844397536\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-919947947 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kWRbVKDkrren5vQ8ASRAS7kZbZAeToYK9Kqbk5Cb</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/youcan/webhook-status</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>app_settings</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>company</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Acme Corporation</span>\"\n      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n      \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n      \"<span class=sf-dump-key>website</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>localization</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>default_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n      \"<span class=sf-dump-key>default_country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">TN</span>\"\n      \"<span class=sf-dump-key>default_language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"3 characters\">UTC</span>\"\n      \"<span class=sf-dump-key>date_format</span>\" => \"<span class=sf-dump-str title=\"5 characters\">d-m-Y</span>\"\n      \"<span class=sf-dump-key>time_format</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>ui</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>datatable_row_limit</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>employees_can_export_data</span>\" => <span class=sf-dump-const>true</span>\n    </samp>]\n    \"<span class=sf-dump-key>currency</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">tunisan dinar</span>\"\n      \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str title=\"2 characters\">DT</span>\"\n      \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"5 characters\">right</span>\"\n      \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n      \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n      \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>3</span>\n    </samp>]\n    \"<span class=sf-dump-key>country</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>iso2</span>\" => \"<span class=sf-dump-str title=\"2 characters\">TN</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Tunisia</span>\"\n      \"<span class=sf-dump-key>phonecode</span>\" => \"<span class=sf-dump-str title=\"3 characters\">216</span>\"\n      \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n      \"<span class=sf-dump-key>currency_symbol</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1578;.&#1583;</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>currencies</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">tunisan dinar</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str title=\"2 characters\">DT</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"5 characters\">right</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>true</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GBP</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">British Pound</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>&#163;</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Euro</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>&#8364;</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">JPY</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Japanese Yen</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>&#165;</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">US Dollar</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>$</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-919947947\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/suppliers", "action_name": "suppliers.index", "controller_action": "App\\Http\\Controllers\\SupplierController@index"}, "badge": null}}