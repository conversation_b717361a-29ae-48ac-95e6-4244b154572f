{"__meta": {"id": "01JXGCEQ3SPW6VNBJAHTCPPK3P", "datetime": "2025-06-11 20:56:07", "utime": **********.545569, "method": "GET", "uri": "/youcan/import", "ip": "127.0.0.1"}, "messages": {"count": 17, "messages": [{"message": "[20:56:03] LOG.info: YouCan CMS Service Initialized {\n    \"base_url\": \"https:\\/\\/api.youcan.shop\",\n    \"client_id\": \"your_client_id\",\n    \"email\": \"<EMAIL>\",\n    \"integration_id\": 1,\n    \"company_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.415749, "xdebug_link": null, "collector": "log"}, {"message": "[20:56:03] LOG.info: Attempting to get YouCan stores {\n    \"integration_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.433715, "xdebug_link": null, "collector": "log"}, {"message": "[20:56:03] LOG.info: Making YouCan API request {\n    \"method\": \"get\",\n    \"url\": \"https:\\/\\/api.youcan.shop\\/stores\",\n    \"data\": [],\n    \"integration_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.435563, "xdebug_link": null, "collector": "log"}, {"message": "[20:56:04] LOG.info: YouCan API response received {\n    \"status\": 200,\n    \"body\": {\n        \"stores\": [\n            {\n                \"store_id\": \"65b1d6e6-f272-41d9-a1bd-13789415c8f9\",\n                \"slug\": \"hustletn\",\n                \"is_active\": true,\n                \"is_email_verified\": true\n            },\n            {\n                \"store_id\": \"c5d51326-ca37-4855-ad59-09ee9d2ed079\",\n                \"slug\": \"hustle77\",\n                \"is_active\": true,\n                \"is_email_verified\": true\n            }\n        ]\n    },\n    \"integration_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": 1749675364.178789, "xdebug_link": null, "collector": "log"}, {"message": "[20:56:04] LOG.info: Stores endpoint response {\n    \"response\": {\n        \"stores\": [\n            {\n                \"store_id\": \"65b1d6e6-f272-41d9-a1bd-13789415c8f9\",\n                \"slug\": \"hustletn\",\n                \"is_active\": true,\n                \"is_email_verified\": true\n            },\n            {\n                \"store_id\": \"c5d51326-ca37-4855-ad59-09ee9d2ed079\",\n                \"slug\": \"hustle77\",\n                \"is_active\": true,\n                \"is_email_verified\": true\n            }\n        ]\n    },\n    \"integration_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": 1749675364.179843, "xdebug_link": null, "collector": "log"}, {"message": "[20:56:04] LOG.info: Making YouCan API request {\n    \"method\": \"post\",\n    \"url\": \"https:\\/\\/api.youcan.shop\\/switch-store\\/65b1d6e6-f272-41d9-a1bd-13789415c8f9\",\n    \"data\": [],\n    \"integration_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": 1749675364.180876, "xdebug_link": null, "collector": "log"}, {"message": "[20:56:05] LOG.info: YouCan API response received {\n    \"status\": 200,\n    \"body\": {\n        \"token\": \"*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\n        \"token_type\": \"Bearer\",\n        \"expired_at\": \"2025-12-21T16:55:41.000000Z\"\n    },\n    \"integration_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": 1749675365.303765, "xdebug_link": null, "collector": "log"}, {"message": "[20:56:05] LOG.info: Making YouCan API request {\n    \"method\": \"get\",\n    \"url\": \"https:\\/\\/api.youcan.shop\\/me\",\n    \"data\": [],\n    \"integration_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": 1749675365.304752, "xdebug_link": null, "collector": "log"}, {"message": "[20:56:05] LOG.info: YouCan API response received {\n    \"status\": 200,\n    \"body\": {\n        \"id\": \"65b1d6e6-f272-41d9-a1bd-13789415c8f9\",\n        \"slug\": \"hustletn\",\n        \"name\": \"HustleTn\",\n        \"is_staff\": false,\n        \"email\": \"<EMAIL>\",\n        \"due_amount\": 0,\n        \"balance\": 0.56,\n        \"unpaid_invoices_amount\": 0,\n        \"store_id\": \"65b1d6e6-f272-41d9-a1bd-13789415c8f9\",\n        \"currency\": {\n            \"code\": \"TND\",\n            \"symbol\": \"\\u062f.\\u062a\"\n        },\n        \"domain\": \"hustletunisie.com\",\n        \"status\": 1,\n        \"status_text\": \"active\",\n        \"closed_at\": null,\n        \"pack_id\": \"b55512dc-7d65-4f70-b5bc-ac30c4abe7fd\",\n        \"logo\": \"https:\\/\\/cdn.youcan.shop\\/stores\\/1e6b6ffd7bfd088c12901ad8dc7ea8d5\\/others\\/piWeFyRSBiioWTPsqPtizQKgEwxeXm4RZAZhfJ0m.png\",\n        \"first_name\": \"Siala\",\n        \"last_name\": \"<PERSON>di\",\n        \"full_name\": \"Siala Majdi\",\n        \"phone\": \"+21622350135\",\n        \"bio\": null,\n        \"website\": null,\n        \"notices\": [],\n        \"is_active\": true,\n        \"is_email_verified\": true,\n        \"can_request\": true,\n        \"can_cancel\": false\n    },\n    \"integration_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": 1749675365.829181, "xdebug_link": null, "collector": "log"}, {"message": "[20:56:05] LOG.info: Successfully got store details {\n    \"store_id\": \"65b1d6e6-f272-41d9-a1bd-13789415c8f9\",\n    \"name\": \"HustleTn\",\n    \"integration_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": 1749675365.83124, "xdebug_link": null, "collector": "log"}, {"message": "[20:56:05] LOG.info: Making YouCan API request {\n    \"method\": \"post\",\n    \"url\": \"https:\\/\\/api.youcan.shop\\/switch-store\\/c5d51326-ca37-4855-ad59-09ee9d2ed079\",\n    \"data\": [],\n    \"integration_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": 1749675365.832698, "xdebug_link": null, "collector": "log"}, {"message": "[20:56:06] LOG.info: YouCan API response received {\n    \"status\": 200,\n    \"body\": {\n        \"token\": \"*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\n        \"token_type\": \"Bearer\",\n        \"expired_at\": \"2026-05-31T11:48:57.000000Z\"\n    },\n    \"integration_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": 1749675366.548875, "xdebug_link": null, "collector": "log"}, {"message": "[20:56:06] LOG.info: Making YouCan API request {\n    \"method\": \"get\",\n    \"url\": \"https:\\/\\/api.youcan.shop\\/me\",\n    \"data\": [],\n    \"integration_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": 1749675366.551056, "xdebug_link": null, "collector": "log"}, {"message": "[20:56:07] LOG.info: YouCan API response received {\n    \"status\": 200,\n    \"body\": {\n        \"id\": \"c5d51326-ca37-4855-ad59-09ee9d2ed079\",\n        \"slug\": \"hustle77\",\n        \"name\": \"<PERSON>B<PERSON>\",\n        \"is_staff\": false,\n        \"email\": \"<EMAIL>\",\n        \"due_amount\": 15.332,\n        \"balance\": 0.87,\n        \"unpaid_invoices_amount\": 0,\n        \"store_id\": \"c5d51326-ca37-4855-ad59-09ee9d2ed079\",\n        \"currency\": {\n            \"code\": \"TND\",\n            \"symbol\": \"\\u062f.\\u062a\"\n        },\n        \"domain\": \"ebk.tn\",\n        \"status\": 1,\n        \"status_text\": \"active\",\n        \"closed_at\": null,\n        \"pack_id\": \"ff15fbe2-111e-11ea-9659-0602d165137c\",\n        \"logo\": \"https:\\/\\/cdn.youcan.shop\\/stores\\/4d0757d3e3777dec0b72a93eee2f5936\\/others\\/EfRjVLrXAp0kFHDB4dz5JVwAxP520ViRFkv3idSS.jpeg\",\n        \"first_name\": \"Siala\",\n        \"last_name\": \"Majdi\",\n        \"full_name\": \"<PERSON>ala <PERSON>di\",\n        \"phone\": \"+21622350135\",\n        \"bio\": null,\n        \"website\": null,\n        \"notices\": [],\n        \"is_active\": true,\n        \"is_email_verified\": true,\n        \"can_request\": true,\n        \"can_cancel\": false\n    },\n    \"integration_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.305101, "xdebug_link": null, "collector": "log"}, {"message": "[20:56:07] LOG.info: Successfully got store details {\n    \"store_id\": \"c5d51326-ca37-4855-ad59-09ee9d2ed079\",\n    \"name\": \"EB<PERSON>\",\n    \"integration_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.306635, "xdebug_link": null, "collector": "log"}, {"message": "[20:56:07] LOG.info: Final stores result {\n    \"stores_count\": 2,\n    \"stores\": [\n        {\n            \"id\": \"65b1d6e6-f272-41d9-a1bd-13789415c8f9\",\n            \"name\": \"HustleTn\",\n            \"slug\": \"hustletn\",\n            \"url\": \"hustletunisie.com\",\n            \"is_active\": true,\n            \"is_default\": false,\n            \"metadata\": {\n                \"store_id\": \"65b1d6e6-f272-41d9-a1bd-13789415c8f9\",\n                \"slug\": \"hustletn\",\n                \"is_active\": true,\n                \"is_email_verified\": true,\n                \"id\": \"65b1d6e6-f272-41d9-a1bd-13789415c8f9\",\n                \"name\": \"HustleTn\",\n                \"is_staff\": false,\n                \"email\": \"<EMAIL>\",\n                \"due_amount\": 0,\n                \"balance\": 0.56,\n                \"unpaid_invoices_amount\": 0,\n                \"currency\": {\n                    \"code\": \"TND\",\n                    \"symbol\": \"\\u062f.\\u062a\"\n                },\n                \"domain\": \"hustletunisie.com\",\n                \"status\": 1,\n                \"status_text\": \"active\",\n                \"closed_at\": null,\n                \"pack_id\": \"b55512dc-7d65-4f70-b5bc-ac30c4abe7fd\",\n                \"logo\": \"https:\\/\\/cdn.youcan.shop\\/stores\\/1e6b6ffd7bfd088c12901ad8dc7ea8d5\\/others\\/piWeFyRSBiioWTPsqPtizQKgEwxeXm4RZAZhfJ0m.png\",\n                \"first_name\": \"Siala\",\n                \"last_name\": \"Majdi\",\n                \"full_name\": \"Siala Majdi\",\n                \"phone\": \"+21622350135\",\n                \"bio\": null,\n                \"website\": null,\n                \"notices\": [],\n                \"can_request\": true,\n                \"can_cancel\": false\n            }\n        },\n        {\n            \"id\": \"c5d51326-ca37-4855-ad59-09ee9d2ed079\",\n            \"name\": \"EBK\",\n            \"slug\": \"hustle77\",\n            \"url\": \"ebk.tn\",\n            \"is_active\": true,\n            \"is_default\": false,\n            \"metadata\": {\n                \"store_id\": \"c5d51326-ca37-4855-ad59-09ee9d2ed079\",\n                \"slug\": \"hustle77\",\n                \"is_active\": true,\n                \"is_email_verified\": true,\n                \"id\": \"c5d51326-ca37-4855-ad59-09ee9d2ed079\",\n                \"name\": \"EBK\",\n                \"is_staff\": false,\n                \"email\": \"<EMAIL>\",\n                \"due_amount\": 15.332,\n                \"balance\": 0.87,\n                \"unpaid_invoices_amount\": 0,\n                \"currency\": {\n                    \"code\": \"TND\",\n                    \"symbol\": \"\\u062f.\\u062a\"\n                },\n                \"domain\": \"ebk.tn\",\n                \"status\": 1,\n                \"status_text\": \"active\",\n                \"closed_at\": null,\n                \"pack_id\": \"ff15fbe2-111e-11ea-9659-0602d165137c\",\n                \"logo\": \"https:\\/\\/cdn.youcan.shop\\/stores\\/4d0757d3e3777dec0b72a93eee2f5936\\/others\\/EfRjVLrXAp0kFHDB4dz5JVwAxP520ViRFkv3idSS.jpeg\",\n                \"first_name\": \"Siala\",\n                \"last_name\": \"Majdi\",\n                \"full_name\": \"Siala Majdi\",\n                \"phone\": \"+21622350135\",\n                \"bio\": null,\n                \"website\": null,\n                \"notices\": [],\n                \"can_request\": true,\n                \"can_cancel\": false\n            }\n        }\n    ],\n    \"integration_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.307793, "xdebug_link": null, "collector": "log"}, {"message": "[20:56:07] LOG.info: YouCan stores synced successfully {\n    \"created\": 0,\n    \"updated\": 2,\n    \"total\": 2,\n    \"integration_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.358757, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1749675362.962117, "end": **********.545822, "duration": 4.583704948425293, "duration_str": "4.58s", "measures": [{"label": "Booting", "start": 1749675362.962117, "relative_start": 0, "end": **********.12609, "relative_end": **********.12609, "duration": 0.*****************, "duration_str": "164ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.12613, "relative_start": 0.*****************, "end": **********.545828, "relative_end": 6.198883056640625e-06, "duration": 4.***************, "duration_str": "4.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.146955, "relative_start": 0.*****************, "end": **********.158914, "relative_end": **********.158914, "duration": 0.011959075927734375, "duration_str": "11.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.406858, "relative_start": 4.****************, "end": **********.542301, "relative_end": **********.542301, "duration": 0.*****************, "duration_str": "135ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: Orders/YoucanImport", "start": **********.528778, "relative_start": 4.**************, "end": **********.528778, "relative_end": **********.528778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 5109464, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.7.2", "PHP Version": "8.2.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "Orders/YoucanImport", "param_count": null, "params": [], "start": **********.52859, "type": "tsx", "hash": "tsxC:\\Users\\<USER>\\Desktop\\react-starter-kit\\resources\\js/Pages/Orders/YoucanImport.tsxOrders/YoucanImport", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fresources%2Fjs%2FPages%2FOrders%2FYoucanImport.tsx&line=1", "ajax": false, "filename": "YoucanImport.tsx", "line": "?"}}]}, "queries": {"count": 14, "nb_statements": 13, "nb_visible_statements": 14, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.16323, "accumulated_duration_str": "163ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 226}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 112}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 92}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 60}, {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 88}], "start": **********.145504, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CacheManager.php:226", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheManager.php&line=226", "ajax": false, "filename": "CacheManager.php", "line": "226"}, "connection": "order_management_system", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = 'fjlxYCyxOlRCexdXwC4uopqp4OZNpD9IdJe0ZzTn' limit 1", "type": "query", "params": [], "bindings": ["fjlxYCyxOlRCexdXwC4uopqp4OZNpD9IdJe0ZzTn"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.1866539, "duration": 0.11170999999999999, "duration_str": "112ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "order_management_system", "explain": null, "start_percent": 0, "width_percent": 68.437}, {"sql": "select * from \"users\" where \"id\" = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.3113198, "duration": 0.01569, "duration_str": "15.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "order_management_system", "explain": null, "start_percent": 68.437, "width_percent": 9.612}, {"sql": "select * from \"companies\" where \"companies\".\"id\" = 1 and \"companies\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Middleware/LoadAppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Middleware\\LoadAppSettings.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Middleware/AddLinkHeadersForPreloadedAssets.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php", "line": 20}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 86}], "start": **********.341099, "duration": 0.00519, "duration_str": "5.19ms", "memory": 0, "memory_str": null, "filename": "LoadAppSettings.php:20", "source": {"index": 21, "namespace": null, "name": "app/Http/Middleware/LoadAppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Middleware\\LoadAppSettings.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FMiddleware%2FLoadAppSettings.php&line=20", "ajax": false, "filename": "LoadAppSettings.php", "line": "20"}, "connection": "order_management_system", "explain": null, "start_percent": 78.049, "width_percent": 3.18}, {"sql": "select * from \"cms_platforms\" where \"slug\" = 'youcan' limit 1", "type": "query", "params": [], "bindings": ["youcan"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.3606439, "duration": 0.00535, "duration_str": "5.35ms", "memory": 0, "memory_str": null, "filename": "YouCanController.php:28", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FYouCanController.php&line=28", "ajax": false, "filename": "YouCanController.php", "line": "28"}, "connection": "order_management_system", "explain": null, "start_percent": 81.229, "width_percent": 3.278}, {"sql": "select * from \"company_cms_integrations\" where \"company_id\" = 1 and \"cms_platform_id\" = 1 limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.382118, "duration": 0.00544, "duration_str": "5.44ms", "memory": 0, "memory_str": null, "filename": "YouCanController.php:37", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FYouCanController.php&line=37", "ajax": false, "filename": "YouCanController.php", "line": "37"}, "connection": "order_management_system", "explain": null, "start_percent": 84.507, "width_percent": 3.333}, {"sql": "select * from \"cms_platforms\" where \"cms_platforms\".\"id\" = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/CmsIntegration/YoucanCmsService.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Services\\CmsIntegration\\YoucanCmsService.php", "line": 22}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.3989449, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "YoucanCmsService.php:22", "source": {"index": 21, "namespace": null, "name": "app/Services/CmsIntegration/YoucanCmsService.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Services\\CmsIntegration\\YoucanCmsService.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FServices%2FCmsIntegration%2FYoucanCmsService.php&line=22", "ajax": false, "filename": "YoucanCmsService.php", "line": "22"}, "connection": "order_management_system", "explain": null, "start_percent": 87.839, "width_percent": 0.576}, {"sql": "select * from \"cache\" where \"key\" in ('laravel_cache_youcan_access_token_1')", "type": "query", "params": [], "bindings": ["laravel_cache_youcan_access_token_1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 118}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 421}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 455}], "start": **********.418709, "duration": 0.00316, "duration_str": "3.16ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "order_management_system", "explain": null, "start_percent": 88.415, "width_percent": 1.936}, {"sql": "select * from \"company_cms_stores\" where (\"company_id\" = 1 and \"company_cms_integration_id\" = 1 and \"store_id\" = '65b1d6e6-f272-41d9-a1bd-13789415c8f9') limit 1", "type": "query", "params": [], "bindings": [1, 1, "65b1d6e6-f272-41d9-a1bd-13789415c8f9"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/CmsIntegration/YoucanCmsService.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Services\\CmsIntegration\\YoucanCmsService.php", "line": 748}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 50}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.310482, "duration": 0.00508, "duration_str": "5.08ms", "memory": 0, "memory_str": null, "filename": "YoucanCmsService.php:748", "source": {"index": 21, "namespace": null, "name": "app/Services/CmsIntegration/YoucanCmsService.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Services\\CmsIntegration\\YoucanCmsService.php", "line": 748}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FServices%2FCmsIntegration%2FYoucanCmsService.php&line=748", "ajax": false, "filename": "YoucanCmsService.php", "line": "748"}, "connection": "order_management_system", "explain": null, "start_percent": 90.351, "width_percent": 3.112}, {"sql": "select * from \"company_cms_stores\" where (\"company_id\" = 1 and \"company_cms_integration_id\" = 1 and \"store_id\" = 'c5d51326-ca37-4855-ad59-09ee9d2ed079') limit 1", "type": "query", "params": [], "bindings": [1, 1, "c5d51326-ca37-4855-ad59-09ee9d2ed079"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/CmsIntegration/YoucanCmsService.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Services\\CmsIntegration\\YoucanCmsService.php", "line": 748}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 50}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.326036, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "YoucanCmsService.php:748", "source": {"index": 21, "namespace": null, "name": "app/Services/CmsIntegration/YoucanCmsService.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Services\\CmsIntegration\\YoucanCmsService.php", "line": 748}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FServices%2FCmsIntegration%2FYoucanCmsService.php&line=748", "ajax": false, "filename": "YoucanCmsService.php", "line": "748"}, "connection": "order_management_system", "explain": null, "start_percent": 93.463, "width_percent": 0.76}, {"sql": "update \"company_cms_stores\" set \"metadata\" = '{\"store_id\":\"c5d51326-ca37-4855-ad59-09ee9d2ed079\",\"slug\":\"hustle77\",\"is_active\":true,\"is_email_verified\":true,\"id\":\"c5d51326-ca37-4855-ad59-09ee9d2ed079\",\"name\":\"EBK\",\"is_staff\":false,\"email\":\"<EMAIL>\",\"due_amount\":15.332,\"balance\":0.87,\"unpaid_invoices_amount\":0,\"currency\":{\"code\":\"TND\",\"symbol\":\"\\u062f.\\u062a\"},\"domain\":\"ebk.tn\",\"status\":1,\"status_text\":\"active\",\"closed_at\":null,\"pack_id\":\"ff15fbe2-111e-11ea-9659-0602d165137c\",\"logo\":\"https:\\/\\/cdn.youcan.shop\\/stores\\/4d0757d3e3777dec0b72a93eee2f5936\\/others\\/EfRjVLrXAp0kFHDB4dz5JVwAxP520ViRFkv3idSS.jpeg\",\"first_name\":\"Siala\",\"last_name\":\"Majdi\",\"full_name\":\"Siala Majdi\",\"phone\":\"+21622350135\",\"bio\":null,\"website\":null,\"notices\":[],\"can_request\":true,\"can_cancel\":false}', \"updated_at\" = '2025-06-11 20:56:07' where \"id\" = 8", "type": "query", "params": [], "bindings": ["{\"store_id\":\"c5d51326-ca37-4855-ad59-09ee9d2ed079\",\"slug\":\"hustle77\",\"is_active\":true,\"is_email_verified\":true,\"id\":\"c5d51326-ca37-4855-ad59-09ee9d2ed079\",\"name\":\"EB<PERSON>\",\"is_staff\":false,\"email\":\"<EMAIL>\",\"due_amount\":15.332,\"balance\":0.87,\"unpaid_invoices_amount\":0,\"currency\":{\"code\":\"TND\",\"symbol\":\"\\u062f.\\u062a\"},\"domain\":\"ebk.tn\",\"status\":1,\"status_text\":\"active\",\"closed_at\":null,\"pack_id\":\"ff15fbe2-111e-11ea-9659-0602d165137c\",\"logo\":\"https:\\/\\/cdn.youcan.shop\\/stores\\/4d0757d3e3777dec0b72a93eee2f5936\\/others\\/EfRjVLrXAp0kFHDB4dz5JVwAxP520ViRFkv3idSS.jpeg\",\"first_name\":\"<PERSON><PERSON>\",\"last_name\":\"<PERSON>di\",\"full_name\":\"<PERSON>ala Majdi\",\"phone\":\"+21622350135\",\"bio\":null,\"website\":null,\"notices\":[],\"can_request\":true,\"can_cancel\":false}", "2025-06-11 20:56:07", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/CmsIntegration/YoucanCmsService.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Services\\CmsIntegration\\YoucanCmsService.php", "line": 748}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 50}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.343975, "duration": 0.004730000000000001, "duration_str": "4.73ms", "memory": 0, "memory_str": null, "filename": "YoucanCmsService.php:748", "source": {"index": 20, "namespace": null, "name": "app/Services/CmsIntegration/YoucanCmsService.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Services\\CmsIntegration\\YoucanCmsService.php", "line": 748}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FServices%2FCmsIntegration%2FYoucanCmsService.php&line=748", "ajax": false, "filename": "YoucanCmsService.php", "line": "748"}, "connection": "order_management_system", "explain": null, "start_percent": 94.223, "width_percent": 2.898}, {"sql": "select * from \"company_cms_integrations\" where \"id\" = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 53}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.3598452, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "YouCanController.php:53", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FYouCanController.php&line=53", "ajax": false, "filename": "YouCanController.php", "line": "53"}, "connection": "order_management_system", "explain": null, "start_percent": 97.121, "width_percent": 0.943}, {"sql": "select * from \"cms_platforms\" where \"cms_platforms\".\"id\" in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 53}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.3741362, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "YouCanController.php:53", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FYouCanController.php&line=53", "ajax": false, "filename": "YouCanController.php", "line": "53"}, "connection": "order_management_system", "explain": null, "start_percent": 98.064, "width_percent": 0.809}, {"sql": "select * from \"company_cms_stores\" where \"company_cms_stores\".\"company_cms_integration_id\" = 1 and \"company_cms_stores\".\"company_cms_integration_id\" is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.389257, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "YouCanController.php:54", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FYouCanController.php&line=54", "ajax": false, "filename": "YouCanController.php", "line": "54"}, "connection": "order_management_system", "explain": null, "start_percent": 98.873, "width_percent": 1.127}]}, "models": {"data": {"App\\Models\\CompanyCmsStore": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FCompanyCmsStore.php&line=1", "ajax": false, "filename": "CompanyCmsStore.php", "line": "?"}}, "App\\Models\\CmsPlatform": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FCmsPlatform.php&line=1", "ajax": false, "filename": "CmsPlatform.php", "line": "?"}}, "App\\Models\\CompanyCmsIntegration": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FCompanyCmsIntegration.php&line=1", "ajax": false, "filename": "CompanyCmsIntegration.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Company": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}}, "count": 11, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/youcan/import", "action_name": "youcan.import", "controller_action": "App\\Http\\Controllers\\YouCanController@showImport", "uri": "GET youcan/import", "controller": "App\\Http\\Controllers\\YouCanController@showImport<a href=\"phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FYouCanController.php&line=23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/youcan", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FYouCanController.php&line=23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/YouCanController.php:23-83</a>", "middleware": "web, auth, verified", "duration": "4.6s", "peak_memory": "6MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-104677958 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-104677958\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-643773443 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-643773443\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1894864646 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">http://127.0.0.1:8000/admin/settings/cms-integrations/youcan</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1603 characters\">_cc_cookie={&quot;categories&quot;:[&quot;necessary&quot;],&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false,&quot;consent_date&quot;:&quot;2025-02-09T00:01:04.160Z&quot;,&quot;consent_uuid&quot;:&quot;410c5564-6d38-4cde-b65a-95ec6886d6c1&quot;,&quot;last_consent_update&quot;:&quot;2025-02-09T00:01:04.160Z&quot;}; locale=en; appearance=light; device_uuid=eyJpdiI6IkducU5Ec2dRdUU2RldQVXpEZjBOaWc9PSIsInZhbHVlIjoiR1hpL2FPTldjeDhodmY1MC92aXV5WXFpeGNiQXpBRWV2VVRkK0IyeHJWSTBoVHF0Nkc4Tjc0VXNWa3BSMHNHWElXa2NrSUd6QjhZSzdnTkpkaEQ0WmtGWWppVGthN293K1N2dHhDZnVkU1ZsNlQ2QXV4VnE0K29COXBnVWQwTktwU3pBb25TeFdIN0l4TWZXbDlUaDdCSndkck5TUW02blptaVVBbXpweVBLOHJFNks3VUxpMU82MkpjenVuUGFPIiwibWFjIjoiZDUwYjkyOWU2OTBiZDhjZGZiOWJhN2ZjM2M4ZGFhMWM0OTcxY2VkMDhkNjlkMWZkMjU3Yzk5MGM1Mzk4ZWFiNyIsInRhZyI6IiJ9; admin-mode=dark; admin-colorPref=dark; direction=false; _ga=GA1.1.1790330909.1748982694; _ga_699NE13B0K=GS2.1.s1749281199$o2$g0$t1749281199$j60$l0$h0; sidebar_state=true; XSRF-TOKEN=eyJpdiI6IlhsRlFZU1ZyQzlydFg0VHI2TDJSZlE9PSIsInZhbHVlIjoibzVyWmwzQk92VGZkTTdDOCtXYkd4SjZWYW1vcmtFdGMybXlXeVZDQ3FrL2JYOXZKbVdnS1RkQ2NXY0RBUG0vNzBadCtzczdhR3NiRnRmR1VLOXA4WENXUm1wQVFQRWNXWE02QlJBUElLRlJ2SXRDU3BlVHBTRHNmOGFmL3JFcDIiLCJtYWMiOiJhODY5ZTgwYjlkMmU2NjAzZTRkMWMyMTA3MGY0ZjhhZmRmNTc5OWNhOThhYjM1NGRiOWQxZjQ1NDhlYmE2NjI0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InlLNUo1TjhOWU9jYm5KcVZmV3B3ZkE9PSIsInZhbHVlIjoiUENCMi95d1g4LzhoZU5DSlVhYnhzTVA2d0R5TzJRQ2ZuY29lWmtoV01YZndVREptMXZYMW42YXpkSk0reitMS3JYREVaVml6dkpHbmxWYXkwUlAyNGRkSDhVOFdVTTJPbG4rRnAwam1ZVlNXMVNhWDNFTzd0R0JWamRneGVHZFYiLCJtYWMiOiIwMWJmNTUxYjZjY2M4OTk5YmJmMGQ5NGM0YmY2NGIyMTM1NzBjODNmY2UwZjk4MjhkMWM4M2IyMWJkODYyMzc5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1894864646\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1581224847 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>device_uuid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-colorPref</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>direction</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_699NE13B0K</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sidebar_state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kWRbVKDkrren5vQ8ASRAS7kZbZAeToYK9Kqbk5Cb</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fjlxYCyxOlRCexdXwC4uopqp4OZNpD9IdJe0ZzTn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1581224847\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-338316191 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 11 Jun 2025 20:56:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-338316191\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-512962438 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kWRbVKDkrren5vQ8ASRAS7kZbZAeToYK9Kqbk5Cb</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://127.0.0.1:8000/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>app_settings</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>company</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Acme Corporation</span>\"\n      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n      \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n      \"<span class=sf-dump-key>website</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>localization</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>default_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n      \"<span class=sf-dump-key>default_country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">TN</span>\"\n      \"<span class=sf-dump-key>default_language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"3 characters\">UTC</span>\"\n      \"<span class=sf-dump-key>date_format</span>\" => \"<span class=sf-dump-str title=\"5 characters\">d-m-Y</span>\"\n      \"<span class=sf-dump-key>time_format</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>ui</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>datatable_row_limit</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>employees_can_export_data</span>\" => <span class=sf-dump-const>true</span>\n    </samp>]\n    \"<span class=sf-dump-key>currency</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">tunisan dinar</span>\"\n      \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str title=\"2 characters\">DT</span>\"\n      \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"5 characters\">right</span>\"\n      \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n      \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n      \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>3</span>\n    </samp>]\n    \"<span class=sf-dump-key>country</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>iso2</span>\" => \"<span class=sf-dump-str title=\"2 characters\">TN</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Tunisia</span>\"\n      \"<span class=sf-dump-key>phonecode</span>\" => \"<span class=sf-dump-str title=\"3 characters\">216</span>\"\n      \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n      \"<span class=sf-dump-key>currency_symbol</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1578;.&#1583;</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>currencies</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">tunisan dinar</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str title=\"2 characters\">DT</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"5 characters\">right</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>true</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GBP</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">British Pound</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>&#163;</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Euro</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>&#8364;</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">JPY</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Japanese Yen</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>&#165;</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">US Dollar</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>$</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-512962438\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/youcan/import", "action_name": "youcan.import", "controller_action": "App\\Http\\Controllers\\YouCanController@showImport"}, "badge": null}}