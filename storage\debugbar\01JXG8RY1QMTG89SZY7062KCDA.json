{"__meta": {"id": "01JXG8RY1QMTG89SZY7062KCDA", "datetime": "2025-06-11 19:51:48", "utime": **********.026944, "method": "GET", "uri": "/customers", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.241344, "end": **********.026964, "duration": 0.7856199741363525, "duration_str": "786ms", "measures": [{"label": "Booting", "start": **********.241344, "relative_start": 0, "end": **********.331204, "relative_end": **********.331204, "duration": 0.****************, "duration_str": "89.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.331221, "relative_start": 0.*****************, "end": **********.026967, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "696ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.340011, "relative_start": 0.*****************, "end": **********.342298, "relative_end": **********.342298, "duration": 0.002287149429321289, "duration_str": "2.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.521403, "relative_start": 0.****************, "end": **********.022987, "relative_end": **********.022987, "duration": 0.****************, "duration_str": "502ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7161824, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.7.2", "PHP Version": "8.2.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "Customers/Index", "param_count": null, "params": [], "start": **********.026698, "type": "tsx", "hash": "tsxC:\\Users\\<USER>\\Desktop\\react-starter-kit\\resources\\js/Pages/Customers/Index.tsxCustomers/Index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fresources%2Fjs%2FPages%2FCustomers%2FIndex.tsx&line=1", "ajax": false, "filename": "Index.tsx", "line": "?"}}]}, "queries": {"count": 10, "nb_statements": 9, "nb_visible_statements": 10, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.08285999999999999, "accumulated_duration_str": "82.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 226}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 112}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 92}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 60}, {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 88}], "start": **********.339405, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CacheManager.php:226", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheManager.php&line=226", "ajax": false, "filename": "CacheManager.php", "line": "226"}, "connection": "order_management_system", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = 'fjlxYCyxOlRCexdXwC4uopqp4OZNpD9IdJe0ZzTn' limit 1", "type": "query", "params": [], "bindings": ["fjlxYCyxOlRCexdXwC4uopqp4OZNpD9IdJe0ZzTn"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.3546512, "duration": 0.048729999999999996, "duration_str": "48.73ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "order_management_system", "explain": null, "start_percent": 0, "width_percent": 58.81}, {"sql": "select * from \"users\" where \"id\" = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.4089708, "duration": 0.01166, "duration_str": "11.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "order_management_system", "explain": null, "start_percent": 58.81, "width_percent": 14.072}, {"sql": "select * from \"companies\" where \"companies\".\"id\" = 1 and \"companies\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Middleware/LoadAppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Middleware\\LoadAppSettings.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Middleware/AddLinkHeadersForPreloadedAssets.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php", "line": 20}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 86}], "start": **********.42596, "duration": 0.00265, "duration_str": "2.65ms", "memory": 0, "memory_str": null, "filename": "LoadAppSettings.php:20", "source": {"index": 21, "namespace": null, "name": "app/Http/Middleware/LoadAppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Middleware\\LoadAppSettings.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FMiddleware%2FLoadAppSettings.php&line=20", "ajax": false, "filename": "LoadAppSettings.php", "line": "20"}, "connection": "order_management_system", "explain": null, "start_percent": 72.882, "width_percent": 3.198}, {"sql": "select count(*) as aggregate from \"customers\" where \"company_id\" = 1 and \"customers\".\"company_id\" = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\CustomerController.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.45295, "duration": 0.00263, "duration_str": "2.63ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:48", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\CustomerController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=48", "ajax": false, "filename": "CustomerController.php", "line": "48"}, "connection": "order_management_system", "explain": null, "start_percent": 76.08, "width_percent": 3.174}, {"sql": "select * from \"customers\" where \"company_id\" = 1 and \"customers\".\"company_id\" = 1 limit 10 offset 0", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\CustomerController.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.460509, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:48", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\CustomerController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=48", "ajax": false, "filename": "CustomerController.php", "line": "48"}, "connection": "order_management_system", "explain": null, "start_percent": 79.254, "width_percent": 1.81}, {"sql": "select * from \"countries\" where \"countries\".\"iso2\" in ('CA', 'MA')", "type": "query", "params": [], "bindings": ["CA", "MA"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\CustomerController.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.4722521, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:48", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\CustomerController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=48", "ajax": false, "filename": "CustomerController.php", "line": "48"}, "connection": "order_management_system", "explain": null, "start_percent": 81.064, "width_percent": 3.15}, {"sql": "select * from \"states\" where \"states\".\"id\" in (866, 867, 3265, 3266)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\CustomerController.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.479326, "duration": 0.00316, "duration_str": "3.16ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:48", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\CustomerController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=48", "ajax": false, "filename": "CustomerController.php", "line": "48"}, "connection": "order_management_system", "explain": null, "start_percent": 84.214, "width_percent": 3.814}, {"sql": "select * from \"cities\" where \"cities\".\"id\" in (15802, 15803, 16152, 16157, 16158, 16160, 16162, 16163)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\CustomerController.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.489165, "duration": 0.00454, "duration_str": "4.54ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:48", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\CustomerController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=48", "ajax": false, "filename": "CustomerController.php", "line": "48"}, "connection": "order_management_system", "explain": null, "start_percent": 88.028, "width_percent": 5.479}, {"sql": "select * from \"countries\" order by \"name\" asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\CustomerController.php", "line": 51}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.498244, "duration": 0.00538, "duration_str": "5.38ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:51", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\CustomerController.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=51", "ajax": false, "filename": "CustomerController.php", "line": "51"}, "connection": "order_management_system", "explain": null, "start_percent": 93.507, "width_percent": 6.493}]}, "models": {"data": {"App\\Models\\Country": {"value": 252, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\Customer": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\City": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FCity.php&line=1", "ajax": false, "filename": "City.php", "line": "?"}}, "App\\Models\\State": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FState.php&line=1", "ajax": false, "filename": "State.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Company": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}}, "count": 276, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/customers", "action_name": "customers.index", "controller_action": "App\\Http\\Controllers\\CustomerController@index", "uri": "GET customers", "controller": "App\\Http\\Controllers\\CustomerController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=28\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=28\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/CustomerController.php:28-58</a>", "middleware": "web, auth, verified", "duration": "789ms", "peak_memory": "8MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-632585628 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-632585628\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-219072083 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-219072083\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2112300913 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InRIZVJhOTZ6SjlGQ1gyUjhybUdaTUE9PSIsInZhbHVlIjoiZmdZZFNDaGhpWWo2NFhiMEczZkFFNVhEMHE3VXZLTUEyS1JrVUQrSlBRcjhKK0UzQVdyU2JtRjMvU25XMEQ5OGUwUVZJdkFZTGdkNnhGWVhpRlYyZEJDak5jRWg2MGVuektRRjIvOE9SczdFMGU1WmZKNndDYTcveWFUZGRUWUQiLCJtYWMiOiI2MTEyMWRhNzE3Y2M2Nzk3N2ExOGI4YzAzOTgyZGMwN2UzOGY4MjkwNDBjMTQ3NzgwZTAyMzM3NGY5MmM2ZWU3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">70490311fe7c84acda8886406a6d884b</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/orders/705</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1603 characters\">_cc_cookie={&quot;categories&quot;:[&quot;necessary&quot;],&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false,&quot;consent_date&quot;:&quot;2025-02-09T00:01:04.160Z&quot;,&quot;consent_uuid&quot;:&quot;410c5564-6d38-4cde-b65a-95ec6886d6c1&quot;,&quot;last_consent_update&quot;:&quot;2025-02-09T00:01:04.160Z&quot;}; locale=en; appearance=light; device_uuid=eyJpdiI6IkducU5Ec2dRdUU2RldQVXpEZjBOaWc9PSIsInZhbHVlIjoiR1hpL2FPTldjeDhodmY1MC92aXV5WXFpeGNiQXpBRWV2VVRkK0IyeHJWSTBoVHF0Nkc4Tjc0VXNWa3BSMHNHWElXa2NrSUd6QjhZSzdnTkpkaEQ0WmtGWWppVGthN293K1N2dHhDZnVkU1ZsNlQ2QXV4VnE0K29COXBnVWQwTktwU3pBb25TeFdIN0l4TWZXbDlUaDdCSndkck5TUW02blptaVVBbXpweVBLOHJFNks3VUxpMU82MkpjenVuUGFPIiwibWFjIjoiZDUwYjkyOWU2OTBiZDhjZGZiOWJhN2ZjM2M4ZGFhMWM0OTcxY2VkMDhkNjlkMWZkMjU3Yzk5MGM1Mzk4ZWFiNyIsInRhZyI6IiJ9; admin-mode=dark; admin-colorPref=dark; direction=false; _ga=GA1.1.1790330909.1748982694; _ga_699NE13B0K=GS2.1.s1749281199$o2$g0$t1749281199$j60$l0$h0; sidebar_state=true; XSRF-TOKEN=eyJpdiI6InRIZVJhOTZ6SjlGQ1gyUjhybUdaTUE9PSIsInZhbHVlIjoiZmdZZFNDaGhpWWo2NFhiMEczZkFFNVhEMHE3VXZLTUEyS1JrVUQrSlBRcjhKK0UzQVdyU2JtRjMvU25XMEQ5OGUwUVZJdkFZTGdkNnhGWVhpRlYyZEJDak5jRWg2MGVuektRRjIvOE9SczdFMGU1WmZKNndDYTcveWFUZGRUWUQiLCJtYWMiOiI2MTEyMWRhNzE3Y2M2Nzk3N2ExOGI4YzAzOTgyZGMwN2UzOGY4MjkwNDBjMTQ3NzgwZTAyMzM3NGY5MmM2ZWU3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjlUWlVoTlpxcGNicWU3a2dpVCtFYXc9PSIsInZhbHVlIjoiR0VPSWxHMStRZ1Vnck10Zmc1aEJ0RlQzdHpjckI4Y1ArTkhBWWhqWkVJMWpqUWZGZVhzbDJuVDlEQWtwdDNNazUxeDZ2RDhuRGJwR3FOOWV1OWN1SkNtRGFaNVNWckg2Q3pDTlVUOWYzYjB0VVJ0eERqQUlTYWNnTm9kR0gzc3QiLCJtYWMiOiI0NzA2NzhjZjU3ODAzMGZkOWFiMjg0MjA2YWM1NGVlNTJiYWZkNGMwNzhmN2QwMDkxNTRmNTNjZDM3NTY3NTRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2112300913\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-988825297 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>device_uuid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-colorPref</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>direction</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_699NE13B0K</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sidebar_state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kWRbVKDkrren5vQ8ASRAS7kZbZAeToYK9Kqbk5Cb</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fjlxYCyxOlRCexdXwC4uopqp4OZNpD9IdJe0ZzTn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-988825297\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1133610534 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 11 Jun 2025 19:51:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1133610534\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1169616965 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kWRbVKDkrren5vQ8ASRAS7kZbZAeToYK9Kqbk5Cb</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>app_settings</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>company</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Acme Corporation</span>\"\n      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n      \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n      \"<span class=sf-dump-key>website</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>localization</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>default_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n      \"<span class=sf-dump-key>default_country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">TN</span>\"\n      \"<span class=sf-dump-key>default_language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"3 characters\">UTC</span>\"\n      \"<span class=sf-dump-key>date_format</span>\" => \"<span class=sf-dump-str title=\"5 characters\">d-m-Y</span>\"\n      \"<span class=sf-dump-key>time_format</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>ui</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>datatable_row_limit</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>employees_can_export_data</span>\" => <span class=sf-dump-const>true</span>\n    </samp>]\n    \"<span class=sf-dump-key>currency</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">tunisan dinar</span>\"\n      \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str title=\"2 characters\">DT</span>\"\n      \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"5 characters\">right</span>\"\n      \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n      \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n      \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>3</span>\n    </samp>]\n    \"<span class=sf-dump-key>country</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>iso2</span>\" => \"<span class=sf-dump-str title=\"2 characters\">TN</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Tunisia</span>\"\n      \"<span class=sf-dump-key>phonecode</span>\" => \"<span class=sf-dump-str title=\"3 characters\">216</span>\"\n      \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n      \"<span class=sf-dump-key>currency_symbol</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1578;.&#1583;</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>currencies</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">tunisan dinar</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str title=\"2 characters\">DT</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"5 characters\">right</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>true</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GBP</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">British Pound</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>&#163;</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Euro</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>&#8364;</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">JPY</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Japanese Yen</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>&#165;</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">US Dollar</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>$</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169616965\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/customers", "action_name": "customers.index", "controller_action": "App\\Http\\Controllers\\CustomerController@index"}, "badge": null}}