{"__meta": {"id": "01JXG8WFPVXJG0Y568DGXXVB3T", "datetime": "2025-06-11 19:53:44", "utime": **********.412284, "method": "POST", "uri": "/youcan/test-connection", "ip": "127.0.0.1"}, "messages": {"count": 3, "messages": [{"message": "[19:53:44] LOG.info: YouCan CMS Service Initialized {\n    \"base_url\": \"https:\\/\\/api.youcan.shop\",\n    \"client_id\": \"your_client_id\",\n    \"email\": \"<EMAIL>\",\n    \"integration_id\": 1,\n    \"company_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.192089, "xdebug_link": null, "collector": "log"}, {"message": "[19:53:44] LOG.info: Making YouCan API request {\n    \"method\": \"get\",\n    \"url\": \"https:\\/\\/api.youcan.shop\\/orders\\/settings\",\n    \"data\": [],\n    \"integration_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.200091, "xdebug_link": null, "collector": "log"}, {"message": "[19:53:44] LOG.info: YouCan API response received {\n    \"status\": 200,\n    \"body\": [],\n    \"integration_id\": 1\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.405879, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1749671623.957307, "end": **********.412495, "duration": 0.4551877975463867, "duration_str": "455ms", "measures": [{"label": "Booting", "start": 1749671623.957307, "relative_start": 0, "end": **********.026573, "relative_end": **********.026573, "duration": 0.*****************, "duration_str": "69.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.026592, "relative_start": 0.*****************, "end": **********.412513, "relative_end": 1.811981201171875e-05, "duration": 0.*****************, "duration_str": "386ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.037957, "relative_start": 0.*****************, "end": **********.040488, "relative_end": **********.040488, "duration": 0.0025310516357421875, "duration_str": "2.53ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.409373, "relative_start": 0.*****************, "end": **********.410108, "relative_end": **********.410108, "duration": 0.0007350444793701172, "duration_str": "735μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 3895784, "peak_usage_str": "4MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.7.2", "PHP Version": "8.2.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 8, "nb_statements": 7, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07901999999999999, "accumulated_duration_str": "79.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 226}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 112}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 92}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 60}, {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 88}], "start": **********.037071, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CacheManager.php:226", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheManager.php&line=226", "ajax": false, "filename": "CacheManager.php", "line": "226"}, "connection": "order_management_system", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = 'fjlxYCyxOlRCexdXwC4uopqp4OZNpD9IdJe0ZzTn' limit 1", "type": "query", "params": [], "bindings": ["fjlxYCyxOlRCexdXwC4uopqp4OZNpD9IdJe0ZzTn"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.064783, "duration": 0.05913, "duration_str": "59.13ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "order_management_system", "explain": null, "start_percent": 0, "width_percent": 74.829}, {"sql": "select * from \"users\" where \"id\" = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.133922, "duration": 0.00645, "duration_str": "6.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "order_management_system", "explain": null, "start_percent": 74.829, "width_percent": 8.162}, {"sql": "select * from \"companies\" where \"companies\".\"id\" = 1 and \"companies\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Middleware/LoadAppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Middleware\\LoadAppSettings.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Middleware/AddLinkHeadersForPreloadedAssets.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php", "line": 20}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 86}], "start": **********.1500611, "duration": 0.0030800000000000003, "duration_str": "3.08ms", "memory": 0, "memory_str": null, "filename": "LoadAppSettings.php:20", "source": {"index": 21, "namespace": null, "name": "app/Http/Middleware/LoadAppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Middleware\\LoadAppSettings.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FMiddleware%2FLoadAppSettings.php&line=20", "ajax": false, "filename": "LoadAppSettings.php", "line": "20"}, "connection": "order_management_system", "explain": null, "start_percent": 82.992, "width_percent": 3.898}, {"sql": "select * from \"cms_platforms\" where \"slug\" = 'youcan' limit 1", "type": "query", "params": [], "bindings": ["youcan"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 94}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.1592839, "duration": 0.00441, "duration_str": "4.41ms", "memory": 0, "memory_str": null, "filename": "YouCanController.php:94", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FYouCanController.php&line=94", "ajax": false, "filename": "YouCanController.php", "line": "94"}, "connection": "order_management_system", "explain": null, "start_percent": 86.889, "width_percent": 5.581}, {"sql": "select * from \"company_cms_integrations\" where \"company_id\" = 1 and \"cms_platform_id\" = 1 limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 106}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.1718311, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "YouCanController.php:106", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 106}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FYouCanController.php&line=106", "ajax": false, "filename": "YouCanController.php", "line": "106"}, "connection": "order_management_system", "explain": null, "start_percent": 92.47, "width_percent": 4.113}, {"sql": "select * from \"cms_platforms\" where \"cms_platforms\".\"id\" = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/CmsIntegration/YoucanCmsService.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Services\\CmsIntegration\\YoucanCmsService.php", "line": 22}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/YouCanController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\YouCanController.php", "line": 116}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.1850052, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "YoucanCmsService.php:22", "source": {"index": 21, "namespace": null, "name": "app/Services/CmsIntegration/YoucanCmsService.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Services\\CmsIntegration\\YoucanCmsService.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FServices%2FCmsIntegration%2FYoucanCmsService.php&line=22", "ajax": false, "filename": "YoucanCmsService.php", "line": "22"}, "connection": "order_management_system", "explain": null, "start_percent": 96.583, "width_percent": 1.228}, {"sql": "select * from \"cache\" where \"key\" in ('laravel_cache_youcan_access_token_1')", "type": "query", "params": [], "bindings": ["laravel_cache_youcan_access_token_1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 118}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 421}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 455}], "start": **********.19303, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "order_management_system", "explain": null, "start_percent": 97.811, "width_percent": 2.189}]}, "models": {"data": {"App\\Models\\CmsPlatform": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FCmsPlatform.php&line=1", "ajax": false, "filename": "CmsPlatform.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Company": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\CompanyCmsIntegration": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FCompanyCmsIntegration.php&line=1", "ajax": false, "filename": "CompanyCmsIntegration.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/youcan/test-connection", "action_name": "youcan.test-connection", "controller_action": "App\\Http\\Controllers\\YouCanController@testConnection", "uri": "POST youcan/test-connection", "controller": "App\\Http\\Controllers\\YouCanController@testConnection<a href=\"phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FYouCanController.php&line=88\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/youcan", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FYouCanController.php&line=88\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/YouCanController.php:88-139</a>", "middleware": "web, auth, verified", "duration": "465ms", "peak_memory": "6MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1472628306 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1472628306\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-608202763 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-608202763\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2010688133 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkxXVGZEYi9Mdk9ZaXl3TllMdXVXNVE9PSIsInZhbHVlIjoibVlMZ0R2NUhLZVpiT1NMN2g5anBZaFlhTlhNckcxQTg5QkJHeUw1R0tIa3l4OWFWR0FkNTJ2aUlnL3UyYjVQQmhudGFCUDczODJPeVF0L1JRcWtScnhWS3YyYVNPZDN1UThZanhWeTlHMnlyYUVRZlA0WjZXeUlMMVZ0R1RjcEsiLCJtYWMiOiI5ZmY1OThjOTA0M2Q5YWIyMTczYmE2YTI0ZDZmZTUwMWEzYTU2N2NjZjE1ODI1OGUyNDEwZmNkMDZjYTllMjZhIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/youcan/import</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1603 characters\">_cc_cookie={&quot;categories&quot;:[&quot;necessary&quot;],&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false,&quot;consent_date&quot;:&quot;2025-02-09T00:01:04.160Z&quot;,&quot;consent_uuid&quot;:&quot;410c5564-6d38-4cde-b65a-95ec6886d6c1&quot;,&quot;last_consent_update&quot;:&quot;2025-02-09T00:01:04.160Z&quot;}; locale=en; appearance=light; device_uuid=eyJpdiI6IkducU5Ec2dRdUU2RldQVXpEZjBOaWc9PSIsInZhbHVlIjoiR1hpL2FPTldjeDhodmY1MC92aXV5WXFpeGNiQXpBRWV2VVRkK0IyeHJWSTBoVHF0Nkc4Tjc0VXNWa3BSMHNHWElXa2NrSUd6QjhZSzdnTkpkaEQ0WmtGWWppVGthN293K1N2dHhDZnVkU1ZsNlQ2QXV4VnE0K29COXBnVWQwTktwU3pBb25TeFdIN0l4TWZXbDlUaDdCSndkck5TUW02blptaVVBbXpweVBLOHJFNks3VUxpMU82MkpjenVuUGFPIiwibWFjIjoiZDUwYjkyOWU2OTBiZDhjZGZiOWJhN2ZjM2M4ZGFhMWM0OTcxY2VkMDhkNjlkMWZkMjU3Yzk5MGM1Mzk4ZWFiNyIsInRhZyI6IiJ9; admin-mode=dark; admin-colorPref=dark; direction=false; _ga=GA1.1.1790330909.1748982694; _ga_699NE13B0K=GS2.1.s1749281199$o2$g0$t1749281199$j60$l0$h0; sidebar_state=true; XSRF-TOKEN=eyJpdiI6IkxXVGZEYi9Mdk9ZaXl3TllMdXVXNVE9PSIsInZhbHVlIjoibVlMZ0R2NUhLZVpiT1NMN2g5anBZaFlhTlhNckcxQTg5QkJHeUw1R0tIa3l4OWFWR0FkNTJ2aUlnL3UyYjVQQmhudGFCUDczODJPeVF0L1JRcWtScnhWS3YyYVNPZDN1UThZanhWeTlHMnlyYUVRZlA0WjZXeUlMMVZ0R1RjcEsiLCJtYWMiOiI5ZmY1OThjOTA0M2Q5YWIyMTczYmE2YTI0ZDZmZTUwMWEzYTU2N2NjZjE1ODI1OGUyNDEwZmNkMDZjYTllMjZhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ijkva1N0K0ZoQmhOalhXRW9qU095bmc9PSIsInZhbHVlIjoiZXRFTnBHN2tUeDdqM3o5VHJlalZCN0gra2E2b3g1anBXaXNQaWdBeE81WDZpZTR3VGtGK3IyVm5zMGF5ZVBVWkMwcWl4S05hSXc5MEFnV3FJNGVMN250SW9WNWUxU0hUQWhPZ0FaVFUwa1RBZDV1Vnl1STE1UGFQNThhcEdjRjYiLCJtYWMiOiIxZjMwOGY3MjljODYwZTRhODJlOTM1ZWEwNTQ1ZjAzZjMyNGJjOGIyOTVhZWFlZTQyMTIwNzQ1ZDY5NGEyMGE0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2010688133\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-610221745 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>device_uuid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-colorPref</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>direction</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_699NE13B0K</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sidebar_state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kWRbVKDkrren5vQ8ASRAS7kZbZAeToYK9Kqbk5Cb</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fjlxYCyxOlRCexdXwC4uopqp4OZNpD9IdJe0ZzTn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-610221745\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1146934740 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 11 Jun 2025 19:53:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1146934740\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1537611930 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kWRbVKDkrren5vQ8ASRAS7kZbZAeToYK9Kqbk5Cb</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/youcan/webhook-status</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>app_settings</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>company</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Acme Corporation</span>\"\n      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n      \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n      \"<span class=sf-dump-key>website</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>localization</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>default_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n      \"<span class=sf-dump-key>default_country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">TN</span>\"\n      \"<span class=sf-dump-key>default_language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"3 characters\">UTC</span>\"\n      \"<span class=sf-dump-key>date_format</span>\" => \"<span class=sf-dump-str title=\"5 characters\">d-m-Y</span>\"\n      \"<span class=sf-dump-key>time_format</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>ui</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>datatable_row_limit</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>employees_can_export_data</span>\" => <span class=sf-dump-const>true</span>\n    </samp>]\n    \"<span class=sf-dump-key>currency</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">tunisan dinar</span>\"\n      \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str title=\"2 characters\">DT</span>\"\n      \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"5 characters\">right</span>\"\n      \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n      \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n      \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>3</span>\n    </samp>]\n    \"<span class=sf-dump-key>country</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>iso2</span>\" => \"<span class=sf-dump-str title=\"2 characters\">TN</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Tunisia</span>\"\n      \"<span class=sf-dump-key>phonecode</span>\" => \"<span class=sf-dump-str title=\"3 characters\">216</span>\"\n      \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n      \"<span class=sf-dump-key>currency_symbol</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1578;.&#1583;</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>currencies</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">tunisan dinar</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str title=\"2 characters\">DT</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"5 characters\">right</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>true</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GBP</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">British Pound</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>&#163;</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Euro</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>&#8364;</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">JPY</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Japanese Yen</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>&#165;</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">US Dollar</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>$</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1537611930\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/youcan/test-connection", "action_name": "youcan.test-connection", "controller_action": "App\\Http\\Controllers\\YouCanController@testConnection"}, "badge": null}}