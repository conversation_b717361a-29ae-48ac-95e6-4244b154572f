<?php

use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Settings\ProfileController;
use App\Http\Controllers\Settings\PasswordController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\CustomerController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\Api\StateController;
use App\Http\Controllers\Api\CityController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\Api\ProductVariantController;
use App\Http\Controllers\SupplierController;
use App\Http\Controllers\WarehouseController;
use App\Http\Controllers\AdPlatformController;
use App\Http\Controllers\AdCampaignController;
use App\Http\Controllers\OrderStatusController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\Api\LocationController;
use App\Http\Controllers\YoucanWebhookController;
use App\Http\Controllers\Admin\CmsIntegrationsController;
use App\Http\Controllers\YouCanController;
use App\Http\Controllers\ShopifyController;
use App\Http\Controllers\AppSettingsController;
use App\Http\Controllers\CompanySettingsController;
use App\Http\Controllers\CurrencyController;

// Guest routes
Route::middleware('guest')->group(function () {
    Route::get('/', function () {
        return Inertia::render('Welcome');
    })->name('welcome');

    Route::get('register', [RegisteredUserController::class, 'create'])->name('register');
    Route::post('register', [RegisteredUserController::class, 'store']);
});

// Protected routes for authenticated users
Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard (home for authenticated users)
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Users Management
    Route::resource('users', UserController::class);
    
    // Roles Management
    Route::resource('roles', RoleController::class);
    
    // Settings Routes
    Route::get('settings', function() {
        return redirect()->route('profile.edit');
    })->name('settings');

    Route::get('settings/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('settings/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('settings/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    Route::get('settings/password', [PasswordController::class, 'edit'])->name('password.edit');
    Route::put('settings/password', [PasswordController::class, 'update'])->name('password.update');

    Route::get('settings/appearance', function () {
        return Inertia::render('settings/appearance');
    })->name('appearance');

    Route::get('settings/app-settings', [AppSettingsController::class, 'index'])->name('settings.app-settings');
    Route::put('settings/app-settings', [AppSettingsController::class, 'update'])->name('settings.app-settings.update');

    Route::get('settings/company-settings', [CompanySettingsController::class, 'index'])->name('settings.company-settings');
    Route::put('settings/company-settings', [CompanySettingsController::class, 'update'])->name('settings.company-settings.update');

    Route::get('settings/currency-settings', [CurrencyController::class, 'index'])->name('settings.currency-settings');
    Route::post('settings/currency-settings', [CurrencyController::class, 'store'])->name('settings.currency-settings.store');
    Route::put('settings/currency-settings/{currency}', [CurrencyController::class, 'update'])->name('settings.currency-settings.update');
    Route::delete('settings/currency-settings/{currency}', [CurrencyController::class, 'destroy'])->name('settings.currency-settings.destroy');
    Route::patch('settings/currency-settings/{currency}/set-default', [CurrencyController::class, 'setDefault'])->name('settings.currency-settings.set-default');
    Route::get('api/predefined-currencies', [CurrencyController::class, 'getPredefinedCurrencies'])->name('api.predefined-currencies');

    // Test route for app settings
    Route::get('test-app-settings', function () {
        return Inertia::render('test-app-settings');
    })->name('test-app-settings');

    // CMS Integrations Routes
    Route::prefix('admin/settings/cms-integrations')->name('cms-integrations.')->group(function () {
        Route::get('/', [CmsIntegrationsController::class, 'index'])->name('index');
        Route::get('/api/integrations', [CmsIntegrationsController::class, 'getIntegrations'])->name('api.integrations');
        Route::get('/youcan', [CmsIntegrationsController::class, 'showYoucan'])->name('youcan.show');
        Route::post('/youcan', [CmsIntegrationsController::class, 'storeYoucan'])->name('youcan.store');
        Route::get('/shopify', [CmsIntegrationsController::class, 'showShopify'])->name('shopify.show');
        Route::post('/shopify', [CmsIntegrationsController::class, 'storeShopify'])->name('shopify.store');
        Route::post('/{platform}/test', [CmsIntegrationsController::class, 'testConnection'])->name('test');
        Route::delete('/{platform}', [CmsIntegrationsController::class, 'destroy'])->name('destroy');
    });

    // Customer lookup by phone
    Route::get('/customers/lookup', [OrderController::class, 'getCustomerByPhone'])->name('customers.lookup');

    // Location data
    Route::get('/location/data', [LocationController::class, 'getData'])->name('location.data');

    // Customer Routes
    Route::resource('customers', CustomerController::class);
    Route::post('customers/import', [CustomerController::class, 'import'])->name('customers.import');
    Route::get('customers/export', [CustomerController::class, 'export'])->name('customers.export');

    Route::get('/states/{country}', [StateController::class, 'index']);

// Cities API
Route::get('/cities/{state}', [CityController::class, 'index']);

    // Products
    Route::get('/products', [ProductController::class, 'index'])->name('products.index');
    Route::get('/products/create', [ProductController::class, 'create'])->name('products.create');
    Route::post('/products', [ProductController::class, 'store'])->name('products.store');
    Route::get('/products/{product}', [ProductController::class, 'show'])->name('products.show');
    Route::get('/products/{product}/edit', [ProductController::class, 'edit'])->name('products.edit');
    Route::put('/products/{product}', [ProductController::class, 'update'])->name('products.update');
    Route::delete('/products/{product}', [ProductController::class, 'destroy'])->name('products.destroy');

    // Product Variants
    Route::get('/products/{product}/variants', [ProductVariantController::class, 'index'])->name('product-variants.index');
    Route::get('/products/{product}/variants/create', [ProductVariantController::class, 'create'])->name('product-variants.create');
    Route::post('/products/{product}/variants', [ProductVariantController::class, 'store'])->name('product-variants.store');
    Route::get('/products/{product}/variants/{variant}', [ProductVariantController::class, 'show'])->name('product-variants.show');
    Route::get('/products/{product}/variants/{variant}/edit', [ProductVariantController::class, 'edit'])->name('product-variants.edit');
    Route::put('/products/{product}/variants/{variant}', [ProductVariantController::class, 'update'])->name('product-variants.update');
    Route::delete('/products/{product}/variants/{variant}', [ProductVariantController::class, 'destroy'])->name('product-variants.destroy');

    // Warehouses
    Route::get('/warehouses', [WarehouseController::class, 'index'])->name('warehouses.index');
    Route::get('/warehouses/create', [WarehouseController::class, 'create'])->name('warehouses.create');
    Route::post('/warehouses', [WarehouseController::class, 'store'])->name('warehouses.store');
    Route::get('/warehouses/{warehouse}', [WarehouseController::class, 'show'])->name('warehouses.show');
    Route::get('/warehouses/{warehouse}/edit', [WarehouseController::class, 'edit'])->name('warehouses.edit');
    Route::put('/warehouses/{warehouse}', [WarehouseController::class, 'update'])->name('warehouses.update');
    Route::delete('/warehouses/{warehouse}', [WarehouseController::class, 'destroy'])->name('warehouses.destroy');

    // Suppliers
    Route::get('/suppliers', [SupplierController::class, 'index'])->name('suppliers.index');
    Route::get('/suppliers/create', [SupplierController::class, 'create'])->name('suppliers.create');
    Route::post('/suppliers', [SupplierController::class, 'store'])->name('suppliers.store');
    Route::get('/suppliers/{supplier}', [SupplierController::class, 'show'])->name('suppliers.show');
    Route::get('/suppliers/{supplier}/edit', [SupplierController::class, 'edit'])->name('suppliers.edit');
    Route::put('/suppliers/{supplier}', [SupplierController::class, 'update'])->name('suppliers.update');
    Route::delete('/suppliers/{supplier}', [SupplierController::class, 'destroy'])->name('suppliers.destroy');

    // Ad Platforms
    Route::get('/ad-platforms', [AdPlatformController::class, 'index'])->name('ad-platforms.index');
    Route::get('/ad-platforms/create', [AdPlatformController::class, 'create'])->name('ad-platforms.create');
    Route::post('/ad-platforms', [AdPlatformController::class, 'store'])->name('ad-platforms.store');
    Route::get('/ad-platforms/{adPlatform}', [AdPlatformController::class, 'show'])->name('ad-platforms.show');
    Route::get('/ad-platforms/{adPlatform}/edit', [AdPlatformController::class, 'edit'])->name('ad-platforms.edit');
    Route::put('/ad-platforms/{adPlatform}', [AdPlatformController::class, 'update'])->name('ad-platforms.update');
    Route::delete('/ad-platforms/{adPlatform}', [AdPlatformController::class, 'destroy'])->name('ad-platforms.destroy');

    // Ad Campaigns
    Route::get('/ad-campaigns', [AdCampaignController::class, 'index'])->name('ad-campaigns.index');
    Route::get('/ad-campaigns/create', [AdCampaignController::class, 'create'])->name('ad-campaigns.create');
    Route::post('/ad-campaigns', [AdCampaignController::class, 'store'])->name('ad-campaigns.store');
    Route::get('/ad-campaigns/{adCampaign}', [AdCampaignController::class, 'show'])->name('ad-campaigns.show');
    Route::get('/ad-campaigns/{adCampaign}/edit', [AdCampaignController::class, 'edit'])->name('ad-campaigns.edit');
    Route::put('/ad-campaigns/{adCampaign}', [AdCampaignController::class, 'update'])->name('ad-campaigns.update');
    Route::delete('/ad-campaigns/{adCampaign}', [AdCampaignController::class, 'destroy'])->name('ad-campaigns.destroy');

    Route::resource('order-statuses', OrderStatusController::class);

    // Backward compatibility redirects for old YouCan routes
    Route::get('orders/youcan-import', function () {
        return redirect()->route('youcan.import');
    });

    // YouCan routes
    Route::prefix('youcan')->name('youcan.')->group(function () {
        Route::get('import', [YouCanController::class, 'showImport'])->name('import');
        Route::post('test-connection', [YouCanController::class, 'testConnection'])->name('test-connection');
        Route::post('import-orders', [YouCanController::class, 'importOrders'])->name('import-orders');
        Route::post('sync-products', [YouCanController::class, 'syncProducts'])->name('sync-products');
        Route::post('send-products', [YouCanController::class, 'sendProducts'])->name('send-products');
        Route::post('send-customers', [YouCanController::class, 'sendCustomers'])->name('send-customers');
        Route::post('sync-customers', [YouCanController::class, 'syncCustomers'])->name('sync-customers');
        Route::post('switch-store/{storeId}', [YouCanController::class, 'switchStore'])->name('switch-store');
        Route::post('sync-stores', [YouCanController::class, 'syncStores'])->name('sync-stores');
        Route::get('current-store', [YouCanController::class, 'getCurrentStore'])->name('current-store');
        Route::get('set-session-test', [YouCanController::class, 'setSessionTest'])->name('set-session-test');

        // Webhook management routes
        Route::get('webhook-status', [YoucanWebhookController::class, 'status'])->name('webhook-status');
        Route::post('webhook-subscribe', [YoucanWebhookController::class, 'subscribe'])->name('webhook-subscribe');
        Route::post('webhook-unsubscribe', [YoucanWebhookController::class, 'unsubscribe'])->name('webhook-unsubscribe');
    });

    // Shopify routes
    Route::prefix('shopify')->name('shopify.')->group(function () {
        Route::get('import', [ShopifyController::class, 'showImport'])->name('import');
        Route::post('test-connection', [ShopifyController::class, 'testConnection'])->name('test-connection');
        Route::post('import-orders', [ShopifyController::class, 'importOrders'])->name('import-orders');
        Route::post('sync-products', [ShopifyController::class, 'syncProducts'])->name('sync-products');
        Route::post('sync-customers', [ShopifyController::class, 'syncCustomers'])->name('sync-customers');
        Route::get('shop-info', [ShopifyController::class, 'getShopInfo'])->name('shop-info');
        Route::get('webhook-status', [ShopifyController::class, 'getWebhookStatus'])->name('webhook-status');
        Route::post('webhook-subscribe', [ShopifyController::class, 'subscribeWebhook'])->name('webhook-subscribe');
        Route::post('webhook-unsubscribe', [ShopifyController::class, 'unsubscribeWebhook'])->name('webhook-unsubscribe');
    });

    // Orders Routes
    Route::get('/orders', [OrderController::class, 'index'])->name('orders.index');
    Route::get('/orders/create', [OrderController::class, 'create'])->name('orders.create');
    Route::post('/orders', [OrderController::class, 'store'])->name('orders.store');
    Route::get('/orders/{order}', [OrderController::class, 'show'])->name('orders.show')
        ->where('order', '[0-9]+'); // Add constraint to only match numeric IDs
    Route::get('/orders/{order}/edit', [OrderController::class, 'edit'])->name('orders.edit')
        ->where('order', '[0-9]+');
    Route::put('/orders/{order}', [OrderController::class, 'update'])->name('orders.update')
        ->where('order', '[0-9]+');
    Route::delete('/orders/{order}', [OrderController::class, 'destroy'])->name('orders.destroy')
        ->where('order', '[0-9]+');
    
    // Order Confirmation Routes
    Route::get('/orders/{order}/confirmation-details', [OrderController::class, 'getConfirmationDetails'])
        ->name('orders.confirmation-details')
        ->where('order', '[0-9]+');
    Route::post('/orders/{order}/call-attempt', [OrderController::class, 'addCallAttempt'])
        ->name('orders.call-attempt')
        ->where('order', '[0-9]+');
    Route::put('/orders/{order}/items', [OrderController::class, 'updateItems'])
        ->name('orders.update-items')
        ->where('order', '[0-9]+');
});

// YouCan webhook routes moved to api.php

require __DIR__.'/auth.php';
